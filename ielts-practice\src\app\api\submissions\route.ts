import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'
import { z } from 'zod'

// Schema for creating a new submission
const createSubmissionSchema = z.object({
  testId: z.string().min(1, 'Test ID is required'),
})

// Schema for updating submission answers
const updateSubmissionSchema = z.object({
  answers: z.array(z.object({
    questionId: z.string(),
    answer: z.any(),
    isCorrect: z.boolean().optional(),
    points: z.number().optional(),
  })),
  isAutoSaved: z.boolean().default(true),
  timeSpent: z.number().optional(),
})

// Schema for submitting a test
const submitTestSchema = z.object({
  answers: z.array(z.object({
    questionId: z.string(),
    answer: z.any(),
  })),
  timeSpent: z.number().optional(),
})

// GET /api/submissions - Get user's submissions with optional filtering
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const user = await db.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const { searchParams } = new URL(request.url)
    const testId = searchParams.get('testId')
    const status = searchParams.get('status')
    const userId = searchParams.get('userId') // For teachers to view student submissions

    const where: any = {}

    // Students can only see their own submissions
    if (user.role === 'STUDENT') {
      where.userId = user.id
    } else if (userId) {
      // Teachers/admins can view specific user's submissions
      where.userId = userId
    }

    if (testId) where.testId = testId
    if (status) where.status = status

    const submissions = await db.testSubmission.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        test: {
          select: {
            id: true,
            title: true,
            type: true,
            difficulty: true,
            duration: true
          }
        },
        answers: {
          include: {
            question: {
              select: {
                id: true,
                type: true,
                content: true,
                points: true,
                order: true
              }
            }
          },
          orderBy: {
            question: {
              order: 'asc'
            }
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    return NextResponse.json(submissions)
  } catch (error) {
    console.error('Error fetching submissions:', error)
    return NextResponse.json(
      { error: 'Failed to fetch submissions' },
      { status: 500 }
    )
  }
}

// POST /api/submissions - Create a new submission (start a test)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const user = await db.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const body = await request.json()
    const validatedData = createSubmissionSchema.parse(body)

    // Check if test exists and is available
    const test = await db.test.findUnique({
      where: { id: validatedData.testId },
      include: {
        questions: true,
        parts: {
          include: {
            questions: true
          }
        }
      }
    })

    if (!test) {
      return NextResponse.json({ error: 'Test not found' }, { status: 404 })
    }

    if (!test.isPublished || !test.isActive) {
      return NextResponse.json(
        { error: 'Test is not available' },
        { status: 400 }
      )
    }

    // Check if user already has an active submission for this test
    const existingSubmission = await db.testSubmission.findFirst({
      where: {
        userId: user.id,
        testId: validatedData.testId,
        status: 'IN_PROGRESS'
      }
    })

    if (existingSubmission) {
      return NextResponse.json(
        { error: 'You already have an active submission for this test' },
        { status: 400 }
      )
    }

    // Calculate total points
    const allQuestions = [
      ...test.questions,
      ...test.parts.flatMap(part => part.questions)
    ]
    const maxPoints = allQuestions.reduce((sum, q) => sum + q.points, 0)

    const submission = await db.testSubmission.create({
      data: {
        userId: user.id,
        testId: validatedData.testId,
        maxPoints,
        status: 'IN_PROGRESS'
      },
      include: {
        test: {
          select: {
            id: true,
            title: true,
            type: true,
            difficulty: true,
            duration: true
          }
        }
      }
    })

    return NextResponse.json(submission, { status: 201 })
  } catch (error) {
    console.error('Error creating submission:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to create submission' },
      { status: 500 }
    )
  }
}
