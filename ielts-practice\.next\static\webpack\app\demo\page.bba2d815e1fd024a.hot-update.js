"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/demo/page",{

/***/ "(app-pages-browser)/./src/data/authentic-reading-test.ts":
/*!********************************************!*\
  !*** ./src/data/authentic-reading-test.ts ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticReadingTest: () => (/* binding */ authenticReadingTest)\n/* harmony export */ });\nconst authenticReadingTest = {\n    testId: \"ielts-reading-authentic-1\",\n    title: \"IELTS Academic Reading Test\",\n    duration: 60,\n    parts: [\n        {\n            partNumber: 1,\n            title: \"READING PASSAGE 1 - The life and work of Marie Curie\",\n            instructions: \"You should spend about 20 minutes on Questions 1–13, which are based on Reading Passage 1 below.\",\n            questionRange: \"1–13\",\n            passage: \"Marie Curie is probably the most famous woman scientist who has ever lived. Born Maria Sklodowska in Poland in 1867, she is famous for her work on radioactivity, and was twice a winner of the Nobel Prize. With her husband, Pierre Curie, and Henri Becquerel, she was awarded the 1903 Nobel Prize for Physics, and was then sole winner of the 1911 Nobel Prize for Chemistry. She was the first woman to win a Nobel Prize.\\n\\nFrom childhood, Marie was remarkable for her prodigious memory, and at the age of 16 won a gold medal on completion of her secondary education. Because her father lost his savings through bad investment, she then had to take work as a teacher. From her earnings she was able to finance her sister Bronia's medical studies in Paris, on the understanding that Bronia would, in turn, later help her to get an education.\\n\\nIn 1891 this promise was fulfilled and Marie went to Paris and began to study at the Sorbonne (the University of Paris). She often worked far into the night and lived on little more than bread and butter and tea. She came first in the examination in the physical sciences in 1893, and in 1894 was placed second in the examination in mathematical sciences. It was not until the spring of that year that she was introduced to Pierre Curie.\\n\\nTheir marriage in 1895 marked the start of a partnership that was soon to achieve results of world significance. Following Henri Becquerel's discovery in 1896 of a new phenomenon, which Marie later called 'radioactivity', Marie Curie decided to find out if the radioactivity discovered in uranium was to be found in other elements. She discovered that this was true for thorium.\\n\\nTurning her attention to minerals, she found her interest drawn to pitchblende, a mineral whose radioactivity, superior to that of pure uranium, could be explained only by the presence in the ore of small quantities of an unknown substance of very high activity. Pierre Curie joined her in the work that she had undertaken to resolve this problem, and that led to the discovery of the new elements, polonium and radium. While Pierre Curie devoted himself chiefly to the physical study of the new radiations, Marie Curie struggled to obtain pure radium in the metallic state, an undertaking no one else had previously attempted. This was achieved with the help of the chemist Andre-Louis Debierne, one of Pierre Curie's pupils. Based on the results of this research, Marie Curie received her Doctorate of Science, and in 1903 Marie and Pierre shared with Becquerel the Nobel Prize for Physics for the discovery of radioactivity.\\n\\nThe births of Marie's two daughters, Ir\\xe8ne and Eve, in 1897 and 1904 failed to interrupt her scientific work. She was appointed lecturer in physics at the \\xc9cole Normale Sup\\xe9rieure for girls in S\\xe8vres, France (1900), and introduced a method of teaching based on experimental demonstrations. In December 1904 she was appointed chief assistant in the laboratory directed by Pierre Curie.\\n\\nThe sudden death of her husband in 1906 was a bitter blow to Marie Curie, but was also a turning point in her career: henceforth she was to devote all her energy to completing alone the scientific work that they had undertaken. On May 13, 1906, she was appointed to the professorship that had been left vacant on her husband's death, becoming the first woman to teach at the Sorbonne. In 1911 she was awarded the Nobel Prize for Chemistry for the isolation of a pure form of radium.\\n\\nDuring World War I, Marie Curie, with the help of her daughter Ir\\xe8ne, devoted herself to the development of the use of X-radiography, including the mobile units which came to be known as 'Little Curies', used for the treatment of wounded soldiers. In 1918 the Radium Institute, whose staff Ir\\xe8ne had joined, began to operate in earnest, and became a centre for nuclear physics and chemistry. Marie Curie, now at the highest point of her fame and, from 1922, a member of the Academy of Medicine, researched the chemistry of radioactive substances and their medical applications.\\n\\nIn 1921, accompanied by her two daughters, Marie Curie made a triumphant journey to the United States to raise funds for research on radium. Women there presented her with a gram of radium for her campaign. Marie also gave lectures in Belgium, Brazil, Spain and Czechoslovakia and, in addition, had the satisfaction of seeing the development of the Curie Foundation in Paris, and the inauguration in 1932 in Warsaw of the Radium Institute, where her sister Bronia became director.\\n\\nOne of Marie Curie's outstanding achievements was to have understood the need to accumulate intense radioactive sources, not only to treat illness but also to maintain an abundant supply for research. The existence in Paris at the Radium Institute of a stock of 1.5 grams of radium made a decisive contribution to the success of the experiments undertaken in the years around 1930. This work prepared the way for the discovery of the neutron by Sir James Chadwick and, above all, for the discovery in 1934 by Ir\\xe8ne and Fr\\xe9d\\xe9ric Joliot-Curie of artificial radioactivity. A few months after this discovery, Marie Curie died as a result of leukaemia caused by exposure to radiation. She had often carried test tubes containing radioactive isotopes in her pocket, remarking on the pretty blue-green light they gave off.\\n\\nHer contribution to physics had been immense, not only in her own work, the importance of which had been demonstrated by her two Nobel Prizes, but because of her influence on subsequent generations of nuclear physicists and chemists.\",\n            questions: [\n                {\n                    id: 1,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Marie Curie's husband was a joint winner of both Marie's Nobel Prizes.\",\n                    correctAnswer: \"FALSE\",\n                    explanation: \"Pierre was joint winner of the 1903 Nobel Prize for Physics, but Marie was 'sole winner of the 1911 Nobel Prize for Chemistry'.\"\n                },\n                {\n                    id: 2,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Marie became interested in science when she was a child.\",\n                    correctAnswer: \"NOT GIVEN\",\n                    explanation: \"The passage mentions her 'prodigious memory' from childhood but doesn't specify when she became interested in science.\"\n                },\n                {\n                    id: 3,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Marie was able to attend the Sorbonne because of her sister's financial contribution.\",\n                    correctAnswer: \"TRUE\",\n                    explanation: \"Marie financed her sister Bronia's studies 'on the understanding that Bronia would, in turn, later help her to get an education. In 1891 this promise was fulfilled'.\"\n                },\n                {\n                    id: 4,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Marie stopped doing research for several years when her children were born.\",\n                    correctAnswer: \"FALSE\",\n                    explanation: \"The passage states 'The births of Marie's two daughters, Irène and Eve, in 1897 and 1904 failed to interrupt her scientific work'.\"\n                },\n                {\n                    id: 5,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Marie took over the teaching position her husband had held.\",\n                    correctAnswer: \"TRUE\",\n                    explanation: \"After Pierre's death, 'she was appointed to the professorship that had been left vacant on her husband's death'.\"\n                },\n                {\n                    id: 6,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Marie's sister Bronia studied the medical uses of radioactivity.\",\n                    correctAnswer: \"NOT GIVEN\",\n                    explanation: \"The passage mentions Bronia's medical studies and that she became director of the Radium Institute, but doesn't specify she studied medical uses of radioactivity.\"\n                },\n                {\n                    id: 7,\n                    type: 'SENTENCE_COMPLETION',\n                    text: \"When uranium was discovered to be radioactive, Marie Curie found that the element called ___ had the same property.\",\n                    maxWords: 1,\n                    correctAnswer: \"thorium\",\n                    explanation: \"Found in paragraph 4: 'She discovered that this was true for thorium'.\"\n                },\n                {\n                    id: 8,\n                    type: 'SENTENCE_COMPLETION',\n                    text: \"Marie and Pierre Curie's research into the radioactivity of the mineral known as ___ led to the discovery of two new elements.\",\n                    maxWords: 1,\n                    correctAnswer: \"pitchblende\",\n                    explanation: \"Found in paragraph 5: 'she found her interest drawn to pitchblende, a mineral whose radioactivity... led to the discovery of the new elements, polonium and radium'.\"\n                },\n                {\n                    id: 9,\n                    type: 'SENTENCE_COMPLETION',\n                    text: \"In 1911, Marie Curie received recognition for her work on the element ___.\",\n                    maxWords: 1,\n                    correctAnswer: \"radium\",\n                    explanation: \"Found in paragraph 7: 'In 1911 she was awarded the Nobel Prize for Chemistry for the isolation of a pure form of radium'.\"\n                },\n                {\n                    id: 10,\n                    type: 'SENTENCE_COMPLETION',\n                    text: \"Marie and Irène Curie developed X-radiography which was used as a medical technique for ___.\",\n                    maxWords: 1,\n                    correctAnswer: \"soldiers\",\n                    explanation: \"Found in paragraph 8: 'devoted herself to the development of the use of X-radiography... used for the treatment of wounded soldiers'.\"\n                },\n                {\n                    id: 11,\n                    type: 'SENTENCE_COMPLETION',\n                    text: \"Marie Curie saw the importance of collecting radioactive material both for research and for cases of ___.\",\n                    maxWords: 1,\n                    correctAnswer: \"illness\",\n                    explanation: \"Found in paragraph 10: 'understood the need to accumulate intense radioactive sources, not only to treat illness but also to maintain an abundant supply for research'.\"\n                },\n                {\n                    id: 12,\n                    type: 'SENTENCE_COMPLETION',\n                    text: \"The radioactive material stocked in Paris contributed to the discoveries in the 1930s of the ___ and of what was known as artificial radioactivity.\",\n                    maxWords: 1,\n                    correctAnswer: \"neutron\",\n                    explanation: \"Found in paragraph 10: 'This work prepared the way for the discovery of the neutron by Sir James Chadwick'.\"\n                },\n                {\n                    id: 13,\n                    type: 'SENTENCE_COMPLETION',\n                    text: \"During her research, Marie Curie was exposed to radiation and as a result she suffered from ___.\",\n                    maxWords: 1,\n                    correctAnswer: \"leukaemia\",\n                    explanation: \"Found in paragraph 10: 'Marie Curie died as a result of leukaemia caused by exposure to radiation'.\"\n                }\n            ]\n        },\n        {\n            partNumber: 2,\n            title: \"The Development of Plastic Surgery\",\n            instructions: \"Read the text and answer questions 14–26.\",\n            questionRange: \"14–20\",\n            passage: 'Plastic surgery is a medical specialty concerned with the correction or restoration of form and function. While famous for aesthetic surgery, plastic surgery also includes many types of reconstructive surgery, hand surgery, microsurgery, and the treatment of burns.\\n\\nThe word \"plastic\" derives from the Greek plastikos meaning \"to mold\" or \"to shape\"; it is not related to the synthetic polymer material known as plastic. The art and science of plastic surgery has ancient origins. As early as 800 BC, surgeons in India were using skin grafts for reconstructive work, and by 600 BC, they were performing cosmetic surgery. Ancient Romans were also able to perform simple techniques, such as repairing damaged ears.\\n\\nBecause of the social taboo surrounding surgery in the Middle Ages, advances in plastic surgery did not come until the Renaissance period in Europe. Heinrich von Pfolspeundt described a process \"to make a new nose for one who lacks it entirely\" by removing skin from the back of the arm and attaching it to the nose area. By the 15th and 16th centuries, European surgeons were able to carry out skin grafts and other forms of reconstructive surgery.\\n\\nModern plastic surgery developed out of the need to treat facial injuries resulting from World War I. Many servicemen suffered severe facial wounds from shrapnel, and traditional medicine was inadequate to treat such injuries. Harold Gillies, a New Zealand-born surgeon working for the British army, is considered the father of modern plastic surgery. He developed many techniques that are still used today and established the first hospital unit dedicated entirely to plastic surgery.\\n\\nThe development of new techniques continued throughout the 20th century. The introduction of antibiotics greatly reduced the risk of infection, while new anesthetic techniques made longer, more complex operations possible. Today, plastic surgery encompasses a wide range of procedures, from reconstructive work following accidents or cancer treatment to cosmetic procedures designed to enhance appearance.',\n            questions: [\n                {\n                    id: 14,\n                    type: 'MULTIPLE_CHOICE',\n                    text: \"The word 'plastic' in plastic surgery refers to:\",\n                    options: [\n                        \"the use of synthetic materials\",\n                        \"the ability to mold or shape\",\n                        \"a type of medical instrument\",\n                        \"the flexibility of human tissue\"\n                    ]\n                },\n                {\n                    id: 15,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Cosmetic surgery was performed in India before 600 BC.\"\n                },\n                {\n                    id: 16,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Ancient Romans could perform more complex surgery than Indians.\"\n                },\n                {\n                    id: 17,\n                    type: 'FILL_IN_BLANK',\n                    text: \"Heinrich von Pfolspeundt described how to create a new _______ using skin from the arm.\"\n                },\n                {\n                    id: 18,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Harold Gillies was born in Britain.\"\n                },\n                {\n                    id: 19,\n                    type: 'MULTIPLE_CHOICE',\n                    text: \"Modern plastic surgery developed primarily because of:\",\n                    options: [\n                        \"advances in anesthetic techniques\",\n                        \"the need to treat war injuries\",\n                        \"the invention of antibiotics\",\n                        \"increased demand for cosmetic procedures\"\n                    ]\n                },\n                {\n                    id: 20,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Antibiotics made plastic surgery completely safe.\"\n                },\n                {\n                    id: 21,\n                    type: 'MATCHING_HEADINGS',\n                    text: \"Choose the correct heading for section A and move it into the gap.\",\n                    headings: [\n                        \"How a concept from one field of study was applied in another\",\n                        \"A lack of investment in driver training\",\n                        \"Areas of doubt and disagreement between experts\",\n                        \"How different countries have dealt with traffic congestion\",\n                        \"The impact of driver behavior on traffic speed\",\n                        \"A proposal to take control away from the driver\"\n                    ],\n                    usedHeadings: [\n                        2,\n                        4,\n                        5,\n                        6\n                    ] // These headings are already used\n                },\n                {\n                    id: 22,\n                    type: 'MULTIPLE_SELECTION',\n                    text: \"Which TWO statements reflect civil engineers' opinions of the physicists' theories?\",\n                    selectCount: 2,\n                    options: [\n                        \"They fail to take into account road maintenance.\",\n                        \"They may have little to do with everyday traffic behaviour.\",\n                        \"They are inconsistent with chaos theory.\",\n                        \"They do not really describe anything new.\",\n                        \"They can easily be disproved.\"\n                    ]\n                }\n            ]\n        },\n        {\n            partNumber: 3,\n            title: \"The Impact of Social Media on Modern Communication\",\n            instructions: \"Read the text and answer questions 27–40.\",\n            questionRange: \"27–33\",\n            passage: \"Social media has fundamentally transformed the way humans communicate, creating unprecedented opportunities for connection while simultaneously raising concerns about the quality and authenticity of modern interactions. The rise of platforms such as Facebook, Twitter, Instagram, and TikTok has created a global communication network that operates 24 hours a day, seven days a week.\\n\\nOne of the most significant impacts of social media has been the democratization of information sharing. Previously, the dissemination of news and information was controlled by traditional media outlets such as newspapers, television, and radio stations. Today, any individual with internet access can share information instantly with a global audience. This has led to both positive and negative consequences. On the positive side, social movements have been able to organize more effectively, marginalized voices have found platforms to express themselves, and breaking news can be shared in real-time. However, this democratization has also led to the spread of misinformation and the creation of echo chambers where people are exposed only to information that confirms their existing beliefs.\\n\\nThe psychological impact of social media use has become a subject of intense research and debate. Studies have shown that excessive use of social media can lead to increased feelings of anxiety, depression, and social isolation, particularly among young people. The constant comparison with others' curated online personas can create unrealistic expectations and feelings of inadequacy. Furthermore, the addictive nature of social media platforms, designed to maximize user engagement through intermittent reinforcement schedules, has raised concerns about digital wellness and the need for better regulation of these technologies.\\n\\nDespite these concerns, social media has also created new opportunities for education, business, and creative expression. Online learning platforms have made education more accessible, small businesses can reach global markets through social media marketing, and artists and creators can build audiences without traditional gatekeepers. The COVID-19 pandemic highlighted the importance of digital communication tools in maintaining social connections during periods of physical isolation.\\n\\nLooking forward, the challenge will be to harness the benefits of social media while mitigating its negative effects. This will likely require a combination of technological solutions, regulatory frameworks, and digital literacy education to help users navigate the complex landscape of modern digital communication.\",\n            questions: [\n                {\n                    id: 27,\n                    type: 'MULTIPLE_CHOICE',\n                    text: \"According to the passage, social media has democratized information sharing by:\",\n                    options: [\n                        \"replacing traditional media outlets entirely\",\n                        \"allowing anyone with internet access to share information globally\",\n                        \"improving the quality of news reporting\",\n                        \"reducing the cost of information distribution\"\n                    ]\n                },\n                {\n                    id: 28,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Echo chambers are created when people only see information that supports their existing beliefs.\"\n                },\n                {\n                    id: 29,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Young people are more affected by social media's psychological impact than adults.\"\n                },\n                {\n                    id: 30,\n                    type: 'FILL_IN_BLANK',\n                    text: \"Social media platforms use _______ reinforcement schedules to maximize user engagement.\"\n                },\n                {\n                    id: 31,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"The COVID-19 pandemic proved that digital communication is superior to face-to-face interaction.\"\n                },\n                {\n                    id: 32,\n                    type: 'MULTIPLE_CHOICE',\n                    text: \"The passage suggests that addressing social media's negative effects will require:\",\n                    options: [\n                        \"banning social media platforms entirely\",\n                        \"only technological solutions\",\n                        \"a combination of technology, regulation, and education\",\n                        \"returning to traditional media only\"\n                    ]\n                },\n                {\n                    id: 33,\n                    type: 'SHORT_ANSWER',\n                    text: \"Name two positive outcomes of social media's democratization of information sharing mentioned in the passage.\"\n                },\n                {\n                    id: 34,\n                    type: 'SUMMARY_COMPLETION',\n                    text: \"For businesses, the use of complex language can have financial implications. The benefits of plain language can be seen in the case of companies who remove ___ from their forms and achieve ___ as a result.\\n\\nConsumers often complain that they experience a feeling of ___ when trying to put together do-it-yourself products which have not been tested by companies on a ___. In situations where not keeping to the correct procedures could affect safety issues, it is especially important that ___ information is not left out and no assumptions are made about a stage being self-evident or the consumer having a certain amount of ___.\\n\\nLawyers, however, have raised objections to the use of plain English. They feel that it would result in ambiguity in documents and cause people to lose faith in ___, as it would mean departing from language that has been used in the courts for a very long time.\",\n                    maxWords: 2\n                }\n            ]\n        }\n    ]\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/data/authentic-reading-test.ts\n"));

/***/ })

});