{"name": "ielts-practice", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:seed": "tsx prisma/seed.ts", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:push": "prisma db push"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@prisma/client": "^6.11.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "next": "15.3.5", "next-auth": "^4.24.11", "prisma": "^6.11.1", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "tailwindcss": "^4", "tsx": "^4.20.3", "tw-animate-css": "^1.3.5", "typescript": "^5"}}