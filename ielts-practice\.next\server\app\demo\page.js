/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/demo/page";
exports.ids = ["app/demo/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdemo%2Fpage&page=%2Fdemo%2Fpage&appPaths=%2Fdemo%2Fpage&pagePath=private-next-app-dir%2Fdemo%2Fpage.tsx&appDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CIELTS%20practice%5Cielts-practice%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CIELTS%20practice%5Cielts-practice&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdemo%2Fpage&page=%2Fdemo%2Fpage&appPaths=%2Fdemo%2Fpage&pagePath=private-next-app-dir%2Fdemo%2Fpage.tsx&appDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CIELTS%20practice%5Cielts-practice%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CIELTS%20practice%5Cielts-practice&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/demo/page.tsx */ \"(rsc)/./src/app/demo/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'demo',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/demo/page\",\n        pathname: \"/demo\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdemo%2Fpage&page=%2Fdemo%2Fpage&appPaths=%2Fdemo%2Fpage&pagePath=private-next-app-dir%2Fdemo%2Fpage.tsx&appDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CIELTS%20practice%5Cielts-practice%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CIELTS%20practice%5Cielts-practice&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers.tsx */ \"(rsc)/./src/components/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cdemo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cdemo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/demo/page.tsx */ \"(rsc)/./src/app/demo/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1dpbmRvd3MlMjAxMSU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNJRUxUUyUyMHByYWN0aWNlJTVDJTVDaWVsdHMtcHJhY3RpY2UlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkZW1vJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBKQUFpSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcV2luZG93cyAxMVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxJRUxUUyBwcmFjdGljZVxcXFxpZWx0cy1wcmFjdGljZVxcXFxzcmNcXFxcYXBwXFxcXGRlbW9cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cdemo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV2luZG93cyAxMVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxJRUxUUyBwcmFjdGljZVxcaWVsdHMtcHJhY3RpY2VcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/demo/page.tsx":
/*!*******************************!*\
  !*** ./src/app/demo/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\IELTS practice\\ielts-practice\\src\\app\\demo\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"03f52d364998\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdpbmRvd3MgMTFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcSUVMVFMgcHJhY3RpY2VcXGllbHRzLXByYWN0aWNlXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwM2Y1MmQzNjQ5OThcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers */ \"(rsc)/./src/components/providers.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"IELTS Practice Platform\",\n    description: \"Practice IELTS tests with comprehensive tracking and analytics\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default().variable)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ Providers)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Providers = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\IELTS practice\\ielts-practice\\src\\components\\providers.tsx",
"Providers",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers.tsx */ \"(ssr)/./src/components/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cdemo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cdemo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/demo/page.tsx */ \"(ssr)/./src/app/demo/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1dpbmRvd3MlMjAxMSU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNJRUxUUyUyMHByYWN0aWNlJTVDJTVDaWVsdHMtcHJhY3RpY2UlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkZW1vJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBKQUFpSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcV2luZG93cyAxMVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxJRUxUUyBwcmFjdGljZVxcXFxpZWx0cy1wcmFjdGljZVxcXFxzcmNcXFxcYXBwXFxcXGRlbW9cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cdemo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/demo/page.tsx":
/*!*******************************!*\
  !*** ./src/app/demo/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DemoPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_inspera_reading_interface__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/inspera-reading-interface */ \"(ssr)/./src/components/inspera-reading-interface.tsx\");\n/* harmony import */ var _data_authentic_reading_test__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/authentic-reading-test */ \"(ssr)/./src/data/authentic-reading-test.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DemoPage() {\n    const [showTest, setShowTest] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [answers, setAnswers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const handleAnswerChange = (questionId, answer)=>{\n        setAnswers((prev)=>({\n                ...prev,\n                [questionId]: answer\n            }));\n    };\n    const handleSubmit = ()=>{\n        console.log(\"Test submitted with answers:\", answers);\n        alert(\"Test submitted! Check console for answers.\");\n    };\n    if (showTest) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inspera_reading_interface__WEBPACK_IMPORTED_MODULE_2__.InspecraReadingInterface, {\n            testTakerId: \"12345678\",\n            parts: _data_authentic_reading_test__WEBPACK_IMPORTED_MODULE_3__.authenticReadingTest.parts,\n            answers: answers,\n            onAnswerChange: handleAnswerChange,\n            onSubmit: handleSubmit\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex items-center justify-center p-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-2xl mx-auto text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Authentic IELTS Reading Test\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 mb-8\",\n                            children: \"Experience the exact Inspera interface used in official IELTS test centers worldwide. This demo replicates every detail of the real test environment.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-8 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold text-gray-900 mb-4\",\n                            children: \"Test Information\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4 text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center py-2 border-b\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Duration:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"60 minutes\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center py-2 border-b\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Questions:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"40 questions (33 shown in demo)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center py-2 border-b\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Parts:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"3 reading passages\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center py-2 border-b\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Question Types:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"True/False/Not Given, Multiple Choice, Fill in Blanks\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center py-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Interface:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-600 font-semibold\",\n                                            children: \"Official Inspera Design\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-blue-900 mb-2\",\n                            children: \"\\uD83C\\uDFAF Authentic Features\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"text-sm text-blue-800 space-y-1 text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• Exact IELTS logo and header design\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• Split-screen layout: passage left, questions right\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• Official question numbering and navigation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• Authentic radio button styling\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• Real Cambridge IELTS content\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• Part-by-part navigation system\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• Inspera-style bottom navigation bar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    onClick: ()=>setShowTest(true),\n                    className: \"bg-red-600 hover:bg-red-700 text-white px-8 py-3 text-lg font-medium\",\n                    children: \"Start Authentic Reading Test\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-500 mt-4\",\n                    children: \"This interface is an exact replica of the official IELTS computer-based test system.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/demo/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/inspera-reading-interface.tsx":
/*!******************************************************!*\
  !*** ./src/components/inspera-reading-interface.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InspecraReadingInterface: () => (/* binding */ InspecraReadingInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(ssr)/./src/components/ui/radio-group.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(ssr)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronLeft_ChevronRight_Menu_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronLeft,ChevronRight,Menu,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronLeft_ChevronRight_Menu_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronLeft,ChevronRight,Menu,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronLeft_ChevronRight_Menu_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronLeft,ChevronRight,Menu,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronLeft_ChevronRight_Menu_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronLeft,ChevronRight,Menu,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronLeft_ChevronRight_Menu_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronLeft,ChevronRight,Menu,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* __next_internal_client_entry_do_not_use__ InspecraReadingInterface auto */ \n\n\n\n\n\n\n\nfunction InspecraReadingInterface({ testTakerId, parts, answers, onAnswerChange, onSubmit }) {\n    const [currentPart, setCurrentPart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentQuestionPage, setCurrentQuestionPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const questionsPerPage = 6;\n    const currentPartData = parts[currentPart];\n    const totalQuestions = currentPartData.questions.length;\n    const totalPages = Math.ceil(totalQuestions / questionsPerPage);\n    const startQuestionIndex = currentQuestionPage * questionsPerPage;\n    const endQuestionIndex = Math.min(startQuestionIndex + questionsPerPage, totalQuestions);\n    const currentQuestions = currentPartData.questions.slice(startQuestionIndex, endQuestionIndex);\n    const renderQuestion = (question)=>{\n        const answer = answers[question.id.toString()];\n        switch(question.type){\n            case 'TRUE_FALSE_NOT_GIVEN':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm leading-relaxed\",\n                            children: question.text\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_3__.RadioGroup, {\n                            value: answer || '',\n                            onValueChange: (value)=>onAnswerChange(question.id.toString(), value),\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_3__.RadioGroupItem, {\n                                            value: \"TRUE\",\n                                            id: `${question.id}-true`,\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                            htmlFor: `${question.id}-true`,\n                                            className: \"text-sm font-normal\",\n                                            children: \"TRUE\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_3__.RadioGroupItem, {\n                                            value: \"FALSE\",\n                                            id: `${question.id}-false`,\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                            htmlFor: `${question.id}-false`,\n                                            className: \"text-sm font-normal\",\n                                            children: \"FALSE\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_3__.RadioGroupItem, {\n                                            value: \"NOT GIVEN\",\n                                            id: `${question.id}-not-given`,\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                            htmlFor: `${question.id}-not-given`,\n                                            className: \"text-sm font-normal\",\n                                            children: \"NOT GIVEN\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 11\n                }, this);\n            case 'MULTIPLE_CHOICE':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm leading-relaxed\",\n                            children: question.text\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_3__.RadioGroup, {\n                            value: answer || '',\n                            onValueChange: (value)=>onAnswerChange(question.id.toString(), value),\n                            className: \"space-y-2\",\n                            children: question.options?.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_3__.RadioGroupItem, {\n                                            value: option,\n                                            id: `${question.id}-${index}`,\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                            htmlFor: `${question.id}-${index}`,\n                                            className: \"text-sm font-normal\",\n                                            children: option\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 11\n                }, this);\n            case 'FILL_IN_BLANK':\n            case 'SHORT_ANSWER':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm leading-relaxed\",\n                            children: question.text\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                            value: answer || '',\n                            onChange: (e)=>onAnswerChange(question.id.toString(), e.target.value),\n                            className: \"max-w-xs h-8 text-sm\",\n                            placeholder: \"Type your answer\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 11\n                }, this);\n            case 'SENTENCE_COMPLETION':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm leading-relaxed\",\n                            children: question.text.split('___').map((part, index, array)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        part,\n                                        index < array.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            value: answer || '',\n                                            onChange: (e)=>onAnswerChange(question.id.toString(), e.target.value),\n                                            className: \"inline-block w-32 h-8 text-sm mx-1 text-center\",\n                                            placeholder: question.id.toString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 13\n                        }, this),\n                        question.maxWords && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-500\",\n                            children: [\n                                \"Write NO MORE THAN \",\n                                question.maxWords === 1 ? 'ONE WORD' : `${question.maxWords} WORDS`\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 11\n                }, this);\n            case 'MATCHING_HEADINGS':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm leading-relaxed font-medium\",\n                            children: question.text\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-4 rounded border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-sm mb-3\",\n                                    children: \"List of Headings\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: question.headings?.map((heading, index)=>{\n                                        const headingNumber = index + 1;\n                                        const isUsed = question.usedHeadings?.includes(headingNumber);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `text-sm p-2 rounded ${isUsed ? 'bg-gray-200 text-gray-500' : 'bg-white border'}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium mr-2\",\n                                                    children: String.fromCharCode(97 + index)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 23\n                                                }, this),\n                                                heading\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"text-sm font-medium\",\n                                    children: \"Choose the correct heading:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_3__.RadioGroup, {\n                                    value: answer || '',\n                                    onValueChange: (value)=>onAnswerChange(question.id.toString(), value),\n                                    className: \"space-y-2\",\n                                    children: question.headings?.map((heading, index)=>{\n                                        const headingLetter = String.fromCharCode(97 + index);\n                                        const isUsed = question.usedHeadings?.includes(index + 1);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `flex items-center space-x-2 ${isUsed ? 'opacity-50' : ''}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_3__.RadioGroupItem, {\n                                                    value: headingLetter,\n                                                    id: `${question.id}-${headingLetter}`,\n                                                    className: \"w-4 h-4\",\n                                                    disabled: isUsed\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: `${question.id}-${headingLetter}`,\n                                                    className: \"text-sm font-normal\",\n                                                    children: headingLetter\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 11\n                }, this);\n            case 'MULTIPLE_SELECTION':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm leading-relaxed font-medium\",\n                            children: question.text\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600 mb-3\",\n                            children: [\n                                \"Choose \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: question.selectCount === 2 ? 'TWO' : question.selectCount\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 22\n                                }, this),\n                                \" correct answers.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: question.options?.map((option, index)=>{\n                                const currentAnswers = Array.isArray(answer) ? answer : [];\n                                const isSelected = currentAnswers.includes(option);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: `${question.id}-${index}`,\n                                            checked: isSelected,\n                                            onChange: (e)=>{\n                                                let newAnswers = [\n                                                    ...currentAnswers\n                                                ];\n                                                if (e.target.checked) {\n                                                    if (newAnswers.length < (question.selectCount || 2)) {\n                                                        newAnswers.push(option);\n                                                    }\n                                                } else {\n                                                    newAnswers = newAnswers.filter((a)=>a !== option);\n                                                }\n                                                onAnswerChange(question.id.toString(), newAnswers);\n                                            },\n                                            className: \"w-4 h-4 mt-0.5\",\n                                            disabled: !isSelected && Array.isArray(answer) && answer.length >= (question.selectCount || 2)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                            htmlFor: `${question.id}-${index}`,\n                                            className: \"text-sm font-normal leading-relaxed\",\n                                            children: option\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 11\n                }, this);\n            case 'SUMMARY_COMPLETION':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm leading-relaxed\",\n                            children: question.text.split('___').map((part, index, array)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        part,\n                                        index < array.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            value: answer?.[index] || '',\n                                            onChange: (e)=>{\n                                                const newAnswers = Array.isArray(answer) ? [\n                                                    ...answer\n                                                ] : [];\n                                                newAnswers[index] = e.target.value;\n                                                onAnswerChange(question.id.toString(), newAnswers);\n                                            },\n                                            className: \"inline-block w-24 h-8 text-sm mx-1 text-center\",\n                                            placeholder: (question.id + index).toString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 13\n                        }, this),\n                        question.maxWords && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-500\",\n                            children: [\n                                \"Write NO MORE THAN \",\n                                question.maxWords === 1 ? 'ONE WORD' : `${question.maxWords} WORDS`,\n                                \" from the text for each answer.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 11\n                }, this);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen bg-white flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-3 flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-red-600 font-bold text-xl\",\n                                children: \"IELTS\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Test taker ID\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4 text-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronLeft_ChevronRight_Menu_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronLeft_ChevronRight_Menu_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronLeft_ChevronRight_Menu_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-100 px-6 py-4 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"font-semibold text-gray-900\",\n                        children: [\n                            \"Part \",\n                            currentPartData.partNumber\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-700 mt-1\",\n                        children: currentPartData.instructions\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-1/2 border-r border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-gray-900 mb-4\",\n                                    children: currentPartData.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                                    className: \"h-[calc(100vh-280px)]\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"prose prose-sm max-w-none\",\n                                        children: currentPartData.passage.split('\\n\\n').map((paragraph, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mb-4 text-sm leading-relaxed text-gray-800\",\n                                                children: paragraph\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-900\",\n                                            children: [\n                                                \"Questions \",\n                                                currentPartData.questionRange\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mt-2\",\n                                            children: [\n                                                \"Choose \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"TRUE\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 24\n                                                }, this),\n                                                \" if the statement agrees with the information given in the text, choose \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"FALSE\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 117\n                                                }, this),\n                                                \" if the statement contradicts the information, or choose \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"NOT GIVEN\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 70\n                                                }, this),\n                                                \" if there is no information on this.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                                    className: \"h-[calc(100vh-320px)]\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: currentQuestions.map((question)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-b border-gray-100 pb-4 last:border-b-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-semibold text-gray-900 mt-1 min-w-[20px]\",\n                                                            children: question.id\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: renderQuestion(question)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, question.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                lineNumber: 292,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-t border-gray-200 px-6 py-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: parts.map((part, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setCurrentPart(index);\n                                        setCurrentQuestionPage(0);\n                                    },\n                                    className: `px-3 py-1 text-sm rounded ${currentPart === index ? 'bg-blue-100 text-blue-700 font-medium' : 'text-gray-600 hover:bg-gray-100'}`,\n                                    children: [\n                                        \"Part \",\n                                        part.partNumber\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-sm text-gray-600\",\n                                    children: Array.from({\n                                        length: totalPages\n                                    }, (_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setCurrentQuestionPage(i),\n                                            className: `w-6 h-6 text-xs rounded ${currentQuestionPage === i ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                                            children: i + 1\n                                        }, i, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: [\n                                        \"Part \",\n                                        currentPartData.partNumber,\n                                        \" \\xa0\\xa0 \",\n                                        startQuestionIndex + 1,\n                                        \" of \",\n                                        totalQuestions\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"Part 3 \\xa0\\xa0 0 of 14\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>{\n                                                if (currentQuestionPage > 0) {\n                                                    setCurrentQuestionPage(currentQuestionPage - 1);\n                                                } else if (currentPart > 0) {\n                                                    setCurrentPart(currentPart - 1);\n                                                    setCurrentQuestionPage(Math.ceil(parts[currentPart - 1].questions.length / questionsPerPage) - 1);\n                                                }\n                                            },\n                                            disabled: currentPart === 0 && currentQuestionPage === 0,\n                                            className: \"w-8 h-8 p-0 bg-gray-600 text-white hover:bg-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronLeft_ChevronRight_Menu_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>{\n                                                if (currentQuestionPage < totalPages - 1) {\n                                                    setCurrentQuestionPage(currentQuestionPage + 1);\n                                                } else if (currentPart < parts.length - 1) {\n                                                    setCurrentPart(currentPart + 1);\n                                                    setCurrentQuestionPage(0);\n                                                } else {\n                                                    onSubmit();\n                                                }\n                                            },\n                                            className: \"w-8 h-8 p-0 bg-gray-600 text-white hover:bg-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronLeft_ChevronRight_Menu_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                    lineNumber: 343,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                lineNumber: 342,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n        lineNumber: 271,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/inspera-reading-interface.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\providers.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUVpRDtBQU0xQyxTQUFTQyxVQUFVLEVBQUVDLFFBQVEsRUFBa0I7SUFDcEQscUJBQ0UsOERBQUNGLDREQUFlQTtrQkFDYkU7Ozs7OztBQUdQIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdpbmRvd3MgMTFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcSUVMVFMgcHJhY3RpY2VcXGllbHRzLXByYWN0aWNlXFxzcmNcXGNvbXBvbmVudHNcXHByb3ZpZGVycy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSBcIm5leHQtYXV0aC9yZWFjdFwiXG5cbmludGVyZmFjZSBQcm92aWRlcnNQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFByb3ZpZGVycyh7IGNoaWxkcmVuIH06IFByb3ZpZGVyc1Byb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPFNlc3Npb25Qcm92aWRlcj5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L1Nlc3Npb25Qcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlNlc3Npb25Qcm92aWRlciIsIlByb3ZpZGVycyIsImNoaWxkcmVuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n            destructive: \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n            sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n            lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n            icon: \"size-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nfunction Button({ className, variant, size, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"button\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9idXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBOEI7QUFDYTtBQUNzQjtBQUVqQztBQUVoQyxNQUFNSSxpQkFBaUJGLDZEQUFHQSxDQUN4QiwrYkFDQTtJQUNFRyxVQUFVO1FBQ1JDLFNBQVM7WUFDUEMsU0FDRTtZQUNGQyxhQUNFO1lBQ0ZDLFNBQ0U7WUFDRkMsV0FDRTtZQUNGQyxPQUNFO1lBQ0ZDLE1BQU07UUFDUjtRQUNBQyxNQUFNO1lBQ0pOLFNBQVM7WUFDVE8sSUFBSTtZQUNKQyxJQUFJO1lBQ0pDLE1BQU07UUFDUjtJQUNGO0lBQ0FDLGlCQUFpQjtRQUNmWCxTQUFTO1FBQ1RPLE1BQU07SUFDUjtBQUNGO0FBR0YsU0FBU0ssT0FBTyxFQUNkQyxTQUFTLEVBQ1RiLE9BQU8sRUFDUE8sSUFBSSxFQUNKTyxVQUFVLEtBQUssRUFDZixHQUFHQyxPQUlGO0lBQ0QsTUFBTUMsT0FBT0YsVUFBVW5CLHNEQUFJQSxHQUFHO0lBRTlCLHFCQUNFLDhEQUFDcUI7UUFDQ0MsYUFBVTtRQUNWSixXQUFXaEIsOENBQUVBLENBQUNDLGVBQWU7WUFBRUU7WUFBU087WUFBTU07UUFBVTtRQUN2RCxHQUFHRSxLQUFLOzs7Ozs7QUFHZjtBQUVpQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXaW5kb3dzIDExXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXElFTFRTIHByYWN0aWNlXFxpZWx0cy1wcmFjdGljZVxcc3JjXFxjb21wb25lbnRzXFx1aVxcYnV0dG9uLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgU2xvdCB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3Qtc2xvdFwiXG5pbXBvcnQgeyBjdmEsIHR5cGUgVmFyaWFudFByb3BzIH0gZnJvbSBcImNsYXNzLXZhcmlhbmNlLWF1dGhvcml0eVwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuY29uc3QgYnV0dG9uVmFyaWFudHMgPSBjdmEoXG4gIFwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGdhcC0yIHdoaXRlc3BhY2Utbm93cmFwIHJvdW5kZWQtbWQgdGV4dC1zbSBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWFsbCBkaXNhYmxlZDpwb2ludGVyLWV2ZW50cy1ub25lIGRpc2FibGVkOm9wYWNpdHktNTAgWyZfc3ZnXTpwb2ludGVyLWV2ZW50cy1ub25lIFsmX3N2Zzpub3QoW2NsYXNzKj0nc2l6ZS0nXSldOnNpemUtNCBzaHJpbmstMCBbJl9zdmddOnNocmluay0wIG91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOmJvcmRlci1yaW5nIGZvY3VzLXZpc2libGU6cmluZy1yaW5nLzUwIGZvY3VzLXZpc2libGU6cmluZy1bM3B4XSBhcmlhLWludmFsaWQ6cmluZy1kZXN0cnVjdGl2ZS8yMCBkYXJrOmFyaWEtaW52YWxpZDpyaW5nLWRlc3RydWN0aXZlLzQwIGFyaWEtaW52YWxpZDpib3JkZXItZGVzdHJ1Y3RpdmVcIixcbiAge1xuICAgIHZhcmlhbnRzOiB7XG4gICAgICB2YXJpYW50OiB7XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgXCJiZy1wcmltYXJ5IHRleHQtcHJpbWFyeS1mb3JlZ3JvdW5kIHNoYWRvdy14cyBob3ZlcjpiZy1wcmltYXJ5LzkwXCIsXG4gICAgICAgIGRlc3RydWN0aXZlOlxuICAgICAgICAgIFwiYmctZGVzdHJ1Y3RpdmUgdGV4dC13aGl0ZSBzaGFkb3cteHMgaG92ZXI6YmctZGVzdHJ1Y3RpdmUvOTAgZm9jdXMtdmlzaWJsZTpyaW5nLWRlc3RydWN0aXZlLzIwIGRhcms6Zm9jdXMtdmlzaWJsZTpyaW5nLWRlc3RydWN0aXZlLzQwIGRhcms6YmctZGVzdHJ1Y3RpdmUvNjBcIixcbiAgICAgICAgb3V0bGluZTpcbiAgICAgICAgICBcImJvcmRlciBiZy1iYWNrZ3JvdW5kIHNoYWRvdy14cyBob3ZlcjpiZy1hY2NlbnQgaG92ZXI6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZCBkYXJrOmJnLWlucHV0LzMwIGRhcms6Ym9yZGVyLWlucHV0IGRhcms6aG92ZXI6YmctaW5wdXQvNTBcIixcbiAgICAgICAgc2Vjb25kYXJ5OlxuICAgICAgICAgIFwiYmctc2Vjb25kYXJ5IHRleHQtc2Vjb25kYXJ5LWZvcmVncm91bmQgc2hhZG93LXhzIGhvdmVyOmJnLXNlY29uZGFyeS84MFwiLFxuICAgICAgICBnaG9zdDpcbiAgICAgICAgICBcImhvdmVyOmJnLWFjY2VudCBob3Zlcjp0ZXh0LWFjY2VudC1mb3JlZ3JvdW5kIGRhcms6aG92ZXI6YmctYWNjZW50LzUwXCIsXG4gICAgICAgIGxpbms6IFwidGV4dC1wcmltYXJ5IHVuZGVybGluZS1vZmZzZXQtNCBob3Zlcjp1bmRlcmxpbmVcIixcbiAgICAgIH0sXG4gICAgICBzaXplOiB7XG4gICAgICAgIGRlZmF1bHQ6IFwiaC05IHB4LTQgcHktMiBoYXMtWz5zdmddOnB4LTNcIixcbiAgICAgICAgc206IFwiaC04IHJvdW5kZWQtbWQgZ2FwLTEuNSBweC0zIGhhcy1bPnN2Z106cHgtMi41XCIsXG4gICAgICAgIGxnOiBcImgtMTAgcm91bmRlZC1tZCBweC02IGhhcy1bPnN2Z106cHgtNFwiLFxuICAgICAgICBpY29uOiBcInNpemUtOVwiLFxuICAgICAgfSxcbiAgICB9LFxuICAgIGRlZmF1bHRWYXJpYW50czoge1xuICAgICAgdmFyaWFudDogXCJkZWZhdWx0XCIsXG4gICAgICBzaXplOiBcImRlZmF1bHRcIixcbiAgICB9LFxuICB9XG4pXG5cbmZ1bmN0aW9uIEJ1dHRvbih7XG4gIGNsYXNzTmFtZSxcbiAgdmFyaWFudCxcbiAgc2l6ZSxcbiAgYXNDaGlsZCA9IGZhbHNlLFxuICAuLi5wcm9wc1xufTogUmVhY3QuQ29tcG9uZW50UHJvcHM8XCJidXR0b25cIj4gJlxuICBWYXJpYW50UHJvcHM8dHlwZW9mIGJ1dHRvblZhcmlhbnRzPiAmIHtcbiAgICBhc0NoaWxkPzogYm9vbGVhblxuICB9KSB7XG4gIGNvbnN0IENvbXAgPSBhc0NoaWxkID8gU2xvdCA6IFwiYnV0dG9uXCJcblxuICByZXR1cm4gKFxuICAgIDxDb21wXG4gICAgICBkYXRhLXNsb3Q9XCJidXR0b25cIlxuICAgICAgY2xhc3NOYW1lPXtjbihidXR0b25WYXJpYW50cyh7IHZhcmlhbnQsIHNpemUsIGNsYXNzTmFtZSB9KSl9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKVxufVxuXG5leHBvcnQgeyBCdXR0b24sIGJ1dHRvblZhcmlhbnRzIH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlNsb3QiLCJjdmEiLCJjbiIsImJ1dHRvblZhcmlhbnRzIiwidmFyaWFudHMiLCJ2YXJpYW50IiwiZGVmYXVsdCIsImRlc3RydWN0aXZlIiwib3V0bGluZSIsInNlY29uZGFyeSIsImdob3N0IiwibGluayIsInNpemUiLCJzbSIsImxnIiwiaWNvbiIsImRlZmF1bHRWYXJpYW50cyIsIkJ1dHRvbiIsImNsYXNzTmFtZSIsImFzQ2hpbGQiLCJwcm9wcyIsIkNvbXAiLCJkYXRhLXNsb3QiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nfunction Input({ className, type, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        \"data-slot\": \"input\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\", \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/label.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/label.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-label */ \"(ssr)/./node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Label auto */ \n\n\n\nfunction Label({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        \"data-slot\": \"label\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9sYWJlbC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFOEI7QUFDeUI7QUFFdkI7QUFFaEMsU0FBU0csTUFBTSxFQUNiQyxTQUFTLEVBQ1QsR0FBR0MsT0FDOEM7SUFDakQscUJBQ0UsOERBQUNKLHVEQUFtQjtRQUNsQk0sYUFBVTtRQUNWSCxXQUFXRiw4Q0FBRUEsQ0FDWCx1TkFDQUU7UUFFRCxHQUFHQyxLQUFLOzs7Ozs7QUFHZjtBQUVnQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXaW5kb3dzIDExXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXElFTFRTIHByYWN0aWNlXFxpZWx0cy1wcmFjdGljZVxcc3JjXFxjb21wb25lbnRzXFx1aVxcbGFiZWwudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgKiBhcyBMYWJlbFByaW1pdGl2ZSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LWxhYmVsXCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5mdW5jdGlvbiBMYWJlbCh7XG4gIGNsYXNzTmFtZSxcbiAgLi4ucHJvcHNcbn06IFJlYWN0LkNvbXBvbmVudFByb3BzPHR5cGVvZiBMYWJlbFByaW1pdGl2ZS5Sb290Pikge1xuICByZXR1cm4gKFxuICAgIDxMYWJlbFByaW1pdGl2ZS5Sb290XG4gICAgICBkYXRhLXNsb3Q9XCJsYWJlbFwiXG4gICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICBcImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHRleHQtc20gbGVhZGluZy1ub25lIGZvbnQtbWVkaXVtIHNlbGVjdC1ub25lIGdyb3VwLWRhdGEtW2Rpc2FibGVkPXRydWVdOnBvaW50ZXItZXZlbnRzLW5vbmUgZ3JvdXAtZGF0YS1bZGlzYWJsZWQ9dHJ1ZV06b3BhY2l0eS01MCBwZWVyLWRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBwZWVyLWRpc2FibGVkOm9wYWNpdHktNTBcIixcbiAgICAgICAgY2xhc3NOYW1lXG4gICAgICApfVxuICAgICAgey4uLnByb3BzfVxuICAgIC8+XG4gIClcbn1cblxuZXhwb3J0IHsgTGFiZWwgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiTGFiZWxQcmltaXRpdmUiLCJjbiIsIkxhYmVsIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJSb290IiwiZGF0YS1zbG90Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/label.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/radio-group.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/radio-group.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RadioGroup: () => (/* binding */ RadioGroup),\n/* harmony export */   RadioGroupItem: () => (/* binding */ RadioGroupItem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_radio_group__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-radio-group */ \"(ssr)/./node_modules/@radix-ui/react-radio-group/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_CircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CircleIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ RadioGroup,RadioGroupItem auto */ \n\n\n\n\nfunction RadioGroup({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_radio_group__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        \"data-slot\": \"radio-group\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"grid gap-3\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\radio-group.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\nfunction RadioGroupItem({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_radio_group__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        \"data-slot\": \"radio-group-item\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-input text-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 aspect-square size-4 shrink-0 rounded-full border shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_radio_group__WEBPACK_IMPORTED_MODULE_3__.Indicator, {\n            \"data-slot\": \"radio-group-indicator\",\n            className: \"relative flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"fill-primary absolute top-1/2 left-1/2 size-2 -translate-x-1/2 -translate-y-1/2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\radio-group.tsx\",\n                lineNumber: 39,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\radio-group.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\radio-group.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/radio-group.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/scroll-area.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/scroll-area.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ScrollArea: () => (/* binding */ ScrollArea),\n/* harmony export */   ScrollBar: () => (/* binding */ ScrollBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-scroll-area */ \"(ssr)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ScrollArea,ScrollBar auto */ \n\n\n\nfunction ScrollArea({ className, children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        \"data-slot\": \"scroll-area\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Viewport, {\n                \"data-slot\": \"scroll-area-viewport\",\n                className: \"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScrollBar, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Corner, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\nfunction ScrollBar({ className, orientation = \"vertical\", ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollAreaScrollbar, {\n        \"data-slot\": \"scroll-area-scrollbar\",\n        orientation: orientation,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex touch-none p-px transition-colors select-none\", orientation === \"vertical\" && \"h-full w-2.5 border-l border-l-transparent\", orientation === \"horizontal\" && \"h-2.5 flex-col border-t border-t-transparent\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollAreaThumb, {\n            \"data-slot\": \"scroll-area-thumb\",\n            className: \"bg-border relative flex-1 rounded-full\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/scroll-area.tsx\n");

/***/ }),

/***/ "(ssr)/./src/data/authentic-reading-test.ts":
/*!********************************************!*\
  !*** ./src/data/authentic-reading-test.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticReadingTest: () => (/* binding */ authenticReadingTest)\n/* harmony export */ });\nconst authenticReadingTest = {\n    testId: \"ielts-reading-authentic-1\",\n    title: \"IELTS Academic Reading Test\",\n    duration: 60,\n    parts: [\n        {\n            partNumber: 1,\n            title: \"The life and work of Marie Curie\",\n            instructions: \"Read the text and answer questions 1–13.\",\n            questionRange: \"1–6\",\n            passage: `Marie Curie is probably the most famous woman scientist who has ever lived. Born Maria Sklodowska in Poland in 1867, she is famous for her work on radioactivity, and was twice a winner of the Nobel Prize. With her husband, Pierre Curie, and Henri Becquerel, she was awarded the 1903 Nobel Prize for Physics, and was then sole winner of the 1911 Nobel Prize for Chemistry. She was the first woman to win a Nobel Prize.\n\nFrom childhood, Marie was remarkable for her prodigious memory, and at the age of 16 won a gold medal on completion of her secondary education. Because her father lost his savings through bad investment, she then had to take work as a teacher. From her earnings she was able to finance her sister Bronya's medical studies in Paris, on the understanding that Bronya would, in turn, later help her to get an education.\n\nIn 1891 this promise was fulfilled and Marie went to Paris and began to study at the Sorbonne (the University of Paris). She often worked far into the night and lived on little more than bread and butter and tea. She came first in the examination in the physical sciences in 1893, and in 1894 was placed second in the examination in mathematical sciences. It was not until the spring of that year that she was introduced to Pierre Curie.\n\nTheir marriage in 1895 marked the start of a partnership that was soon to achieve results of world significance. Following Henri Becquerel's discovery in 1896 of the phenomenon that came to be known as radioactivity, Marie Curie decided to investigate whether any other elements gave off this mysterious radiation. She discovered that thorium gave off the same radiation as uranium. Her continued systematic investigations led to the discovery of the new elements radium and polonium, named after her native land. She and her husband were awarded the Nobel Prize for Physics in 1903.\n\nPierre's death in 1906 was a bitter blow to Marie, but it also marked the beginning of her most productive period. She was appointed to her husband's chair at the Sorbonne, thus becoming the first woman professor in the university's 650-year history. The radium institute, which she had been instrumental in founding, was completed in 1914, and it was here that she spent the last 20 years of her life.`,\n            questions: [\n                {\n                    id: 1,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Marie Curie's husband was a joint winner of both Marie's Nobel Prizes.\"\n                },\n                {\n                    id: 2,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Marie became interested in science when she was a child.\"\n                },\n                {\n                    id: 3,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Marie was able to attend the Sorbonne because of her sister's financial contribution.\"\n                },\n                {\n                    id: 4,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Marie stopped doing research for several years when Pierre died.\"\n                },\n                {\n                    id: 5,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"The Radium Institute was built after Marie's death.\"\n                },\n                {\n                    id: 6,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"The Radium Institute was the first research facility of its kind.\"\n                },\n                {\n                    id: 7,\n                    type: 'SENTENCE_COMPLETION',\n                    text: \"When uranium was discovered to be radioactive, Marie Curie found that the element called ___ had the same property.\",\n                    maxWords: 1\n                },\n                {\n                    id: 8,\n                    type: 'SENTENCE_COMPLETION',\n                    text: \"Marie and Pierre Curie's research into the radioactivity of the mineral known as ___ led to the discovery of two new elements.\",\n                    maxWords: 1\n                },\n                {\n                    id: 9,\n                    type: 'SENTENCE_COMPLETION',\n                    text: \"In 1911, Marie Curie received recognition for her work on the element ___.\",\n                    maxWords: 1\n                },\n                {\n                    id: 10,\n                    type: 'SENTENCE_COMPLETION',\n                    text: \"Marie and Irène Curie developed X-radiography which was used as a medical technique for ___.\",\n                    maxWords: 1\n                },\n                {\n                    id: 11,\n                    type: 'SENTENCE_COMPLETION',\n                    text: \"Marie Curie saw the importance of collecting radioactive material both for research and for cases of ___.\",\n                    maxWords: 1\n                },\n                {\n                    id: 12,\n                    type: 'SENTENCE_COMPLETION',\n                    text: \"The radioactive material stocked in Paris contributed to the discoveries in the 1930s of the ___ and of what was known as artificial radioactivity.\",\n                    maxWords: 1\n                },\n                {\n                    id: 13,\n                    type: 'SENTENCE_COMPLETION',\n                    text: \"During her research, Marie Curie was exposed to radiation and as a result she suffered from ___.\",\n                    maxWords: 1\n                }\n            ]\n        },\n        {\n            partNumber: 2,\n            title: \"The Development of Plastic Surgery\",\n            instructions: \"Read the text and answer questions 14–26.\",\n            questionRange: \"14–20\",\n            passage: `Plastic surgery is a medical specialty concerned with the correction or restoration of form and function. While famous for aesthetic surgery, plastic surgery also includes many types of reconstructive surgery, hand surgery, microsurgery, and the treatment of burns.\n\nThe word \"plastic\" derives from the Greek plastikos meaning \"to mold\" or \"to shape\"; it is not related to the synthetic polymer material known as plastic. The art and science of plastic surgery has ancient origins. As early as 800 BC, surgeons in India were using skin grafts for reconstructive work, and by 600 BC, they were performing cosmetic surgery. Ancient Romans were also able to perform simple techniques, such as repairing damaged ears.\n\nBecause of the social taboo surrounding surgery in the Middle Ages, advances in plastic surgery did not come until the Renaissance period in Europe. Heinrich von Pfolspeundt described a process \"to make a new nose for one who lacks it entirely\" by removing skin from the back of the arm and attaching it to the nose area. By the 15th and 16th centuries, European surgeons were able to carry out skin grafts and other forms of reconstructive surgery.\n\nModern plastic surgery developed out of the need to treat facial injuries resulting from World War I. Many servicemen suffered severe facial wounds from shrapnel, and traditional medicine was inadequate to treat such injuries. Harold Gillies, a New Zealand-born surgeon working for the British army, is considered the father of modern plastic surgery. He developed many techniques that are still used today and established the first hospital unit dedicated entirely to plastic surgery.\n\nThe development of new techniques continued throughout the 20th century. The introduction of antibiotics greatly reduced the risk of infection, while new anesthetic techniques made longer, more complex operations possible. Today, plastic surgery encompasses a wide range of procedures, from reconstructive work following accidents or cancer treatment to cosmetic procedures designed to enhance appearance.`,\n            questions: [\n                {\n                    id: 14,\n                    type: 'MULTIPLE_CHOICE',\n                    text: \"The word 'plastic' in plastic surgery refers to:\",\n                    options: [\n                        \"the use of synthetic materials\",\n                        \"the ability to mold or shape\",\n                        \"a type of medical instrument\",\n                        \"the flexibility of human tissue\"\n                    ]\n                },\n                {\n                    id: 15,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Cosmetic surgery was performed in India before 600 BC.\"\n                },\n                {\n                    id: 16,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Ancient Romans could perform more complex surgery than Indians.\"\n                },\n                {\n                    id: 17,\n                    type: 'FILL_IN_BLANK',\n                    text: \"Heinrich von Pfolspeundt described how to create a new _______ using skin from the arm.\"\n                },\n                {\n                    id: 18,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Harold Gillies was born in Britain.\"\n                },\n                {\n                    id: 19,\n                    type: 'MULTIPLE_CHOICE',\n                    text: \"Modern plastic surgery developed primarily because of:\",\n                    options: [\n                        \"advances in anesthetic techniques\",\n                        \"the need to treat war injuries\",\n                        \"the invention of antibiotics\",\n                        \"increased demand for cosmetic procedures\"\n                    ]\n                },\n                {\n                    id: 20,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Antibiotics made plastic surgery completely safe.\"\n                },\n                {\n                    id: 21,\n                    type: 'MATCHING_HEADINGS',\n                    text: \"Choose the correct heading for section A and move it into the gap.\",\n                    headings: [\n                        \"How a concept from one field of study was applied in another\",\n                        \"A lack of investment in driver training\",\n                        \"Areas of doubt and disagreement between experts\",\n                        \"How different countries have dealt with traffic congestion\",\n                        \"The impact of driver behavior on traffic speed\",\n                        \"A proposal to take control away from the driver\"\n                    ],\n                    usedHeadings: [\n                        2,\n                        4,\n                        5,\n                        6\n                    ] // These headings are already used\n                },\n                {\n                    id: 22,\n                    type: 'MULTIPLE_SELECTION',\n                    text: \"Which TWO statements reflect civil engineers' opinions of the physicists' theories?\",\n                    selectCount: 2,\n                    options: [\n                        \"They fail to take into account road maintenance.\",\n                        \"They may have little to do with everyday traffic behaviour.\",\n                        \"They are inconsistent with chaos theory.\",\n                        \"They do not really describe anything new.\",\n                        \"They can easily be disproved.\"\n                    ]\n                }\n            ]\n        },\n        {\n            partNumber: 3,\n            title: \"The Impact of Social Media on Modern Communication\",\n            instructions: \"Read the text and answer questions 27–40.\",\n            questionRange: \"27–33\",\n            passage: `Social media has fundamentally transformed the way humans communicate, creating unprecedented opportunities for connection while simultaneously raising concerns about the quality and authenticity of modern interactions. The rise of platforms such as Facebook, Twitter, Instagram, and TikTok has created a global communication network that operates 24 hours a day, seven days a week.\n\nOne of the most significant impacts of social media has been the democratization of information sharing. Previously, the dissemination of news and information was controlled by traditional media outlets such as newspapers, television, and radio stations. Today, any individual with internet access can share information instantly with a global audience. This has led to both positive and negative consequences. On the positive side, social movements have been able to organize more effectively, marginalized voices have found platforms to express themselves, and breaking news can be shared in real-time. However, this democratization has also led to the spread of misinformation and the creation of echo chambers where people are exposed only to information that confirms their existing beliefs.\n\nThe psychological impact of social media use has become a subject of intense research and debate. Studies have shown that excessive use of social media can lead to increased feelings of anxiety, depression, and social isolation, particularly among young people. The constant comparison with others' curated online personas can create unrealistic expectations and feelings of inadequacy. Furthermore, the addictive nature of social media platforms, designed to maximize user engagement through intermittent reinforcement schedules, has raised concerns about digital wellness and the need for better regulation of these technologies.\n\nDespite these concerns, social media has also created new opportunities for education, business, and creative expression. Online learning platforms have made education more accessible, small businesses can reach global markets through social media marketing, and artists and creators can build audiences without traditional gatekeepers. The COVID-19 pandemic highlighted the importance of digital communication tools in maintaining social connections during periods of physical isolation.\n\nLooking forward, the challenge will be to harness the benefits of social media while mitigating its negative effects. This will likely require a combination of technological solutions, regulatory frameworks, and digital literacy education to help users navigate the complex landscape of modern digital communication.`,\n            questions: [\n                {\n                    id: 27,\n                    type: 'MULTIPLE_CHOICE',\n                    text: \"According to the passage, social media has democratized information sharing by:\",\n                    options: [\n                        \"replacing traditional media outlets entirely\",\n                        \"allowing anyone with internet access to share information globally\",\n                        \"improving the quality of news reporting\",\n                        \"reducing the cost of information distribution\"\n                    ]\n                },\n                {\n                    id: 28,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Echo chambers are created when people only see information that supports their existing beliefs.\"\n                },\n                {\n                    id: 29,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Young people are more affected by social media's psychological impact than adults.\"\n                },\n                {\n                    id: 30,\n                    type: 'FILL_IN_BLANK',\n                    text: \"Social media platforms use _______ reinforcement schedules to maximize user engagement.\"\n                },\n                {\n                    id: 31,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"The COVID-19 pandemic proved that digital communication is superior to face-to-face interaction.\"\n                },\n                {\n                    id: 32,\n                    type: 'MULTIPLE_CHOICE',\n                    text: \"The passage suggests that addressing social media's negative effects will require:\",\n                    options: [\n                        \"banning social media platforms entirely\",\n                        \"only technological solutions\",\n                        \"a combination of technology, regulation, and education\",\n                        \"returning to traditional media only\"\n                    ]\n                },\n                {\n                    id: 33,\n                    type: 'SHORT_ANSWER',\n                    text: \"Name two positive outcomes of social media's democratization of information sharing mentioned in the passage.\"\n                },\n                {\n                    id: 34,\n                    type: 'SUMMARY_COMPLETION',\n                    text: \"For businesses, the use of complex language can have financial implications. The benefits of plain language can be seen in the case of companies who remove ___ from their forms and achieve ___ as a result.\\n\\nConsumers often complain that they experience a feeling of ___ when trying to put together do-it-yourself products which have not been tested by companies on a ___. In situations where not keeping to the correct procedures could affect safety issues, it is especially important that ___ information is not left out and no assumptions are made about a stage being self-evident or the consumer having a certain amount of ___.\\n\\nLawyers, however, have raised objections to the use of plain English. They feel that it would result in ambiguity in documents and cause people to lose faith in ___, as it would mean departing from language that has been used in the courts for a very long time.\",\n                    maxWords: 2\n                }\n            ]\n        }\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/data/authentic-reading-test.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXaW5kb3dzIDExXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXElFTFRTIHByYWN0aWNlXFxpZWx0cy1wcmFjdGljZVxcc3JjXFxsaWJcXHV0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/@radix-ui","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/class-variance-authority"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdemo%2Fpage&page=%2Fdemo%2Fpage&appPaths=%2Fdemo%2Fpage&pagePath=private-next-app-dir%2Fdemo%2Fpage.tsx&appDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CIELTS%20practice%5Cielts-practice%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CIELTS%20practice%5Cielts-practice&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();