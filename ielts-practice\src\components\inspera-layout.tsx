"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  Clock, 
  ChevronLeft, 
  ChevronRight, 
  Flag,
  Eye,
  EyeOff,
  RotateCcw
} from "lucide-react"

interface InspecraLayoutProps {
  testTitle: string
  testType: 'READING' | 'WRITING' | 'LISTENING'
  totalQuestions: number
  currentQuestion: number
  timeLeft: number
  onQuestionChange: (questionNumber: number) => void
  onSubmit: () => void
  onNext: () => void
  onPrevious: () => void
  children: React.ReactNode
  answers: Record<string, any>
  flaggedQuestions?: Set<number>
  onToggleFlag?: (questionNumber: number) => void
  showReview?: boolean
  onToggleReview?: () => void
}

export function InspecraLayout({
  testTitle,
  testType,
  totalQuestions,
  currentQuestion,
  timeLeft,
  onQuestionChange,
  onSubmit,
  onNext,
  onPrevious,
  children,
  answers,
  flaggedQuestions = new Set(),
  onToggleFlag,
  showReview = false,
  onToggleReview
}: InspecraLayoutProps) {
  const [showQuestionPanel, setShowQuestionPanel] = useState(true)

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const mins = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    
    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const getQuestionStatus = (questionNumber: number) => {
    const hasAnswer = answers[questionNumber.toString()] !== undefined && 
                     answers[questionNumber.toString()] !== null && 
                     answers[questionNumber.toString()] !== ''
    const isFlagged = flaggedQuestions.has(questionNumber)
    const isCurrent = questionNumber === currentQuestion

    if (isCurrent) return 'current'
    if (isFlagged) return 'flagged'
    if (hasAnswer) return 'answered'
    return 'unanswered'
  }

  const getQuestionStatusColor = (status: string) => {
    switch (status) {
      case 'current': return 'bg-blue-600 text-white border-blue-600'
      case 'answered': return 'bg-green-100 text-green-800 border-green-300'
      case 'flagged': return 'bg-orange-100 text-orange-800 border-orange-300'
      default: return 'bg-gray-50 text-gray-600 border-gray-200 hover:bg-gray-100'
    }
  }

  const answeredCount = Object.keys(answers).filter(key => {
    const answer = answers[key]
    return answer !== undefined && answer !== null && answer !== ''
  }).length

  const progress = (answeredCount / totalQuestions) * 100

  return (
    <div className="h-screen flex flex-col bg-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-lg font-medium text-gray-900">{testTitle}</h1>
            <Badge variant="outline" className="text-xs">
              {testType}
            </Badge>
          </div>
          
          <div className="flex items-center space-x-6">
            <div className="text-sm text-gray-600">
              Question {currentQuestion} of {totalQuestions}
            </div>
            
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-gray-500" />
              <span className={`font-mono text-lg ${timeLeft < 300 ? 'text-red-600' : 'text-gray-900'}`}>
                {formatTime(timeLeft)}
              </span>
            </div>

            {onToggleReview && (
              <Button
                variant="outline"
                size="sm"
                onClick={onToggleReview}
                className="text-xs"
              >
                {showReview ? <EyeOff className="h-3 w-3 mr-1" /> : <Eye className="h-3 w-3 mr-1" />}
                {showReview ? 'Hide Review' : 'Review'}
              </Button>
            )}
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mt-3">
          <div className="flex justify-between text-xs text-gray-500 mb-1">
            <span>Progress: {answeredCount}/{totalQuestions} answered</span>
            <span>{Math.round(progress)}% complete</span>
          </div>
          <Progress value={progress} className="h-1" />
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Question Navigation Panel */}
        {showQuestionPanel && (
          <div className="w-64 bg-gray-50 border-r border-gray-200 flex flex-col">
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-medium text-gray-900">Questions</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowQuestionPanel(false)}
                  className="h-6 w-6 p-0"
                >
                  <ChevronLeft className="h-3 w-3" />
                </Button>
              </div>
              
              <div className="flex items-center space-x-4 text-xs text-gray-600">
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 bg-green-100 border border-green-300 rounded"></div>
                  <span>Answered</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 bg-orange-100 border border-orange-300 rounded"></div>
                  <span>Flagged</span>
                </div>
              </div>
            </div>
            
            <ScrollArea className="flex-1 p-4">
              <div className="grid grid-cols-5 gap-2">
                {Array.from({ length: totalQuestions }, (_, i) => i + 1).map((questionNumber) => {
                  const status = getQuestionStatus(questionNumber)
                  return (
                    <button
                      key={questionNumber}
                      onClick={() => onQuestionChange(questionNumber)}
                      className={`
                        w-8 h-8 text-xs font-medium border rounded transition-colors
                        ${getQuestionStatusColor(status)}
                      `}
                    >
                      {questionNumber}
                    </button>
                  )
                })}
              </div>
            </ScrollArea>
          </div>
        )}

        {/* Toggle Panel Button */}
        {!showQuestionPanel && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowQuestionPanel(true)}
            className="absolute left-2 top-1/2 transform -translate-y-1/2 z-10 h-12 w-6 p-0 bg-white border border-gray-200 shadow-sm"
          >
            <ChevronRight className="h-3 w-3" />
          </Button>
        )}

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <div className="flex-1 overflow-auto">
            {children}
          </div>

          {/* Bottom Navigation */}
          <div className="bg-white border-t border-gray-200 px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {onToggleFlag && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onToggleFlag(currentQuestion)}
                    className={`text-xs ${
                      flaggedQuestions.has(currentQuestion) 
                        ? 'bg-orange-50 border-orange-300 text-orange-700' 
                        : ''
                    }`}
                  >
                    <Flag className="h-3 w-3 mr-1" />
                    {flaggedQuestions.has(currentQuestion) ? 'Unflag' : 'Flag'}
                  </Button>
                )}
                
                <div className="text-xs text-gray-500">
                  {timeLeft < 300 && (
                    <span className="text-red-600 font-medium">
                      ⚠ Less than 5 minutes remaining
                    </span>
                  )}
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Button
                  variant="outline"
                  onClick={onPrevious}
                  disabled={currentQuestion === 1}
                  className="text-sm"
                >
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  Previous
                </Button>

                {currentQuestion < totalQuestions ? (
                  <Button
                    onClick={onNext}
                    className="text-sm bg-blue-600 hover:bg-blue-700"
                  >
                    Next
                    <ChevronRight className="h-4 w-4 ml-1" />
                  </Button>
                ) : (
                  <Button
                    onClick={onSubmit}
                    className="text-sm bg-green-600 hover:bg-green-700"
                  >
                    Submit Test
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
