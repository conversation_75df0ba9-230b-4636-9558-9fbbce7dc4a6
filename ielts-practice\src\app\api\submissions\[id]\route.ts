import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'
import { z } from 'zod'

// Schema for updating submission answers
const updateSubmissionSchema = z.object({
  answers: z.array(z.object({
    questionId: z.string(),
    answer: z.any(),
  })),
  isAutoSaved: z.boolean().default(true),
  timeSpent: z.number().optional(),
})

// Schema for submitting a test
const submitTestSchema = z.object({
  answers: z.array(z.object({
    questionId: z.string(),
    answer: z.any(),
  })),
  timeSpent: z.number().optional(),
})

// Helper function to calculate score
async function calculateScore(submissionId: string) {
  const submission = await db.testSubmission.findUnique({
    where: { id: submissionId },
    include: {
      answers: {
        include: {
          question: true
        }
      }
    }
  })

  if (!submission) return null

  let totalPoints = 0
  let earnedPoints = 0

  for (const answer of submission.answers) {
    totalPoints += answer.question.points
    
    // Simple scoring logic - can be enhanced based on question type
    const isCorrect = compareAnswers(answer.answer, answer.question.correctAnswer, answer.question.type)
    
    if (isCorrect) {
      earnedPoints += answer.question.points
    }

    // Update the answer with scoring
    await db.submissionAnswer.update({
      where: { id: answer.id },
      data: {
        isCorrect,
        points: isCorrect ? answer.question.points : 0
      }
    })
  }

  const score = totalPoints > 0 ? (earnedPoints / totalPoints) * 100 : 0

  return { score, totalPoints, earnedPoints }
}

// Helper function to compare answers based on question type
function compareAnswers(userAnswer: any, correctAnswer: any, questionType: string): boolean {
  if (!userAnswer) return false

  switch (questionType) {
    case 'MULTIPLE_CHOICE':
    case 'TRUE_FALSE_NOT_GIVEN':
    case 'TRUE_FALSE':
      return userAnswer === correctAnswer

    case 'FILL_IN_BLANK':
    case 'SHORT_ANSWER':
    case 'SENTENCE_COMPLETION':
      if (typeof correctAnswer === 'string' && typeof userAnswer === 'string') {
        return userAnswer.toLowerCase().trim() === correctAnswer.toLowerCase().trim()
      }
      return false

    case 'MULTIPLE_SELECTION':
      if (Array.isArray(userAnswer) && Array.isArray(correctAnswer)) {
        return userAnswer.length === correctAnswer.length &&
               userAnswer.every(answer => correctAnswer.includes(answer))
      }
      return false

    case 'SUMMARY_COMPLETION':
      if (Array.isArray(userAnswer) && Array.isArray(correctAnswer)) {
        return userAnswer.length === correctAnswer.length &&
               userAnswer.every((answer, index) => 
                 answer?.toLowerCase().trim() === correctAnswer[index]?.toLowerCase().trim()
               )
      }
      return false

    default:
      return false
  }
}

// GET /api/submissions/[id] - Get a specific submission
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const user = await db.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const submission = await db.testSubmission.findUnique({
      where: { id: params.id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        test: {
          include: {
            parts: {
              include: {
                questions: {
                  orderBy: { order: 'asc' }
                }
              },
              orderBy: { partNumber: 'asc' }
            },
            questions: {
              orderBy: { order: 'asc' }
            }
          }
        },
        answers: {
          include: {
            question: true
          },
          orderBy: {
            question: {
              order: 'asc'
            }
          }
        }
      }
    })

    if (!submission) {
      return NextResponse.json({ error: 'Submission not found' }, { status: 404 })
    }

    // Check permissions
    if (user.role === 'STUDENT' && submission.userId !== user.id) {
      return NextResponse.json({ error: 'Permission denied' }, { status: 403 })
    }

    return NextResponse.json(submission)
  } catch (error) {
    console.error('Error fetching submission:', error)
    return NextResponse.json(
      { error: 'Failed to fetch submission' },
      { status: 500 }
    )
  }
}

// PUT /api/submissions/[id] - Update submission answers (auto-save)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const user = await db.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const submission = await db.testSubmission.findUnique({
      where: { id: params.id }
    })

    if (!submission) {
      return NextResponse.json({ error: 'Submission not found' }, { status: 404 })
    }

    // Check permissions
    if (submission.userId !== user.id) {
      return NextResponse.json({ error: 'Permission denied' }, { status: 403 })
    }

    if (submission.status !== 'IN_PROGRESS') {
      return NextResponse.json(
        { error: 'Cannot update completed submission' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const validatedData = updateSubmissionSchema.parse(body)

    // Update or create answers
    for (const answerData of validatedData.answers) {
      await db.submissionAnswer.upsert({
        where: {
          submissionId_questionId: {
            submissionId: params.id,
            questionId: answerData.questionId
          }
        },
        update: {
          answer: answerData.answer
        },
        create: {
          submissionId: params.id,
          questionId: answerData.questionId,
          answer: answerData.answer
        }
      })
    }

    // Update submission metadata
    const updatedSubmission = await db.testSubmission.update({
      where: { id: params.id },
      data: {
        isAutoSaved: validatedData.isAutoSaved,
        timeSpent: validatedData.timeSpent,
        updatedAt: new Date()
      },
      include: {
        answers: {
          include: {
            question: true
          }
        }
      }
    })

    return NextResponse.json(updatedSubmission)
  } catch (error) {
    console.error('Error updating submission:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to update submission' },
      { status: 500 }
    )
  }
}
