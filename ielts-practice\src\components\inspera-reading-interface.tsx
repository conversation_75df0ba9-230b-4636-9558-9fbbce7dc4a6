"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { ChevronLeft, ChevronRight, Wifi, Bell, Menu } from "lucide-react"

interface Question {
  id: number
  type: 'TRUE_FALSE_NOT_GIVEN' | 'MULTIPLE_CHOICE' | 'FILL_IN_BLANK' | 'SHORT_ANSWER' | 'SENTENCE_COMPLETION' | 'MATCHING_HEADINGS' | 'MULTIPLE_SELECTION' | 'SUMMARY_COMPLETION'
  text: string
  options?: string[]
  maxWords?: number
  selectCount?: number
  headings?: string[]
  usedHeadings?: number[]
}

interface ReadingPart {
  partNumber: number
  title: string
  instructions: string
  passage: string
  questions: Question[]
  questionRange: string
}

interface InspecraReadingInterfaceProps {
  testTakerId: string
  parts: ReadingPart[]
  answers: Record<string, any>
  onAnswerChange: (questionId: string, answer: any) => void
  onSubmit: () => void
}

export function InspecraReadingInterface({
  testTakerId,
  parts,
  answers,
  onAnswerChange,
  onSubmit
}: InspecraReadingInterfaceProps) {
  const [currentPart, setCurrentPart] = useState(0)
  const [currentQuestionPage, setCurrentQuestionPage] = useState(0)
  
  const questionsPerPage = 6
  const currentPartData = parts[currentPart]
  const totalQuestions = currentPartData.questions.length
  const totalPages = Math.ceil(totalQuestions / questionsPerPage)
  
  const startQuestionIndex = currentQuestionPage * questionsPerPage
  const endQuestionIndex = Math.min(startQuestionIndex + questionsPerPage, totalQuestions)
  const currentQuestions = currentPartData.questions.slice(startQuestionIndex, endQuestionIndex)

  const renderQuestion = (question: Question) => {
    const answer = answers[question.id.toString()]

    switch (question.type) {
      case 'TRUE_FALSE_NOT_GIVEN':
        return (
          <div className="space-y-4">
            <p className="ielts-question">{question.text}</p>
            <RadioGroup
              value={answer || ''}
              onValueChange={(value) => onAnswerChange(question.id.toString(), value)}
              className="space-y-3"
            >
              <div className="flex items-center space-x-3">
                <RadioGroupItem value="TRUE" id={`${question.id}-true`} className="w-4 h-4" />
                <Label htmlFor={`${question.id}-true`} className="ielts-radio-label">TRUE</Label>
              </div>
              <div className="flex items-center space-x-3">
                <RadioGroupItem value="FALSE" id={`${question.id}-false`} className="w-4 h-4" />
                <Label htmlFor={`${question.id}-false`} className="ielts-radio-label">FALSE</Label>
              </div>
              <div className="flex items-center space-x-3">
                <RadioGroupItem value="NOT GIVEN" id={`${question.id}-not-given`} className="w-4 h-4" />
                <Label htmlFor={`${question.id}-not-given`} className="ielts-radio-label">NOT GIVEN</Label>
              </div>
            </RadioGroup>
          </div>
        )

      case 'MULTIPLE_CHOICE':
        return (
          <div className="space-y-4">
            <p className="ielts-question">{question.text}</p>
            <RadioGroup
              value={answer || ''}
              onValueChange={(value) => onAnswerChange(question.id.toString(), value)}
              className="space-y-3"
            >
              {question.options?.map((option, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <RadioGroupItem value={option} id={`${question.id}-${index}`} className="w-4 h-4" />
                  <Label htmlFor={`${question.id}-${index}`} className="ielts-radio-label">{option}</Label>
                </div>
              ))}
            </RadioGroup>
          </div>
        )

      case 'FILL_IN_BLANK':
      case 'SHORT_ANSWER':
        return (
          <div className="space-y-4">
            <p className="ielts-question">{question.text}</p>
            <Input
              value={answer || ''}
              onChange={(e) => onAnswerChange(question.id.toString(), e.target.value)}
              className="max-w-xs h-9 ielts-input"
              placeholder="Type your answer"
            />
          </div>
        )

      case 'SENTENCE_COMPLETION':
        return (
          <div className="space-y-4">
            <div className="ielts-question">
              {question.text.split('___').map((part, index, array) => (
                <span key={index}>
                  {part}
                  {index < array.length - 1 && (
                    <Input
                      value={answer || ''}
                      onChange={(e) => onAnswerChange(question.id.toString(), e.target.value)}
                      className="inline-block w-32 h-9 ielts-input mx-1 text-center"
                      placeholder={question.id.toString()}
                    />
                  )}
                </span>
              ))}
            </div>
            {question.maxWords && (
              <p className="text-sm ielts-low-contrast">
                Write NO MORE THAN {question.maxWords === 1 ? 'ONE WORD' : `${question.maxWords} WORDS`}
              </p>
            )}
          </div>
        )

      case 'MATCHING_HEADINGS':
        return (
          <div className="space-y-5">
            <p className="ielts-question font-medium">{question.text}</p>
            <div className="bg-gray-50 p-5 rounded border">
              <h4 className="ielts-instructions font-medium mb-4">List of Headings</h4>
              <div className="space-y-3">
                {question.headings?.map((heading, index) => {
                  const headingNumber = index + 1
                  const isUsed = question.usedHeadings?.includes(headingNumber)
                  return (
                    <div key={index} className={`ielts-question p-3 rounded ${isUsed ? 'bg-gray-200 text-gray-500' : 'bg-white border'}`}>
                      <span className="font-medium mr-3">{String.fromCharCode(97 + index)}</span>
                      {heading}
                    </div>
                  )
                })}
              </div>
            </div>
            <div className="space-y-3">
              <Label className="ielts-instructions font-medium">Choose the correct heading:</Label>
              <RadioGroup
                value={answer || ''}
                onValueChange={(value) => onAnswerChange(question.id.toString(), value)}
                className="space-y-3"
              >
                {question.headings?.map((heading, index) => {
                  const headingLetter = String.fromCharCode(97 + index)
                  const isUsed = question.usedHeadings?.includes(index + 1)
                  return (
                    <div key={index} className={`flex items-center space-x-3 ${isUsed ? 'opacity-50' : ''}`}>
                      <RadioGroupItem
                        value={headingLetter}
                        id={`${question.id}-${headingLetter}`}
                        className="w-4 h-4"
                        disabled={isUsed}
                      />
                      <Label htmlFor={`${question.id}-${headingLetter}`} className="ielts-radio-label">
                        {headingLetter}
                      </Label>
                    </div>
                  )
                })}
              </RadioGroup>
            </div>
          </div>
        )

      case 'MULTIPLE_SELECTION':
        return (
          <div className="space-y-4">
            <p className="ielts-question font-medium">{question.text}</p>
            <p className="ielts-instructions">
              Choose <strong>{question.selectCount === 2 ? 'TWO' : question.selectCount}</strong> correct answers.
            </p>
            <div className="space-y-3">
              {question.options?.map((option, index) => {
                const currentAnswers = Array.isArray(answer) ? answer : []
                const isSelected = currentAnswers.includes(option)
                return (
                  <div key={index} className="flex items-start space-x-3">
                    <input
                      type="checkbox"
                      id={`${question.id}-${index}`}
                      checked={isSelected}
                      onChange={(e) => {
                        let newAnswers = [...currentAnswers]
                        if (e.target.checked) {
                          if (newAnswers.length < (question.selectCount || 2)) {
                            newAnswers.push(option)
                          }
                        } else {
                          newAnswers = newAnswers.filter(a => a !== option)
                        }
                        onAnswerChange(question.id.toString(), newAnswers)
                      }}
                      className="w-4 h-4 mt-1"
                      disabled={!isSelected && Array.isArray(answer) && answer.length >= (question.selectCount || 2)}
                    />
                    <Label htmlFor={`${question.id}-${index}`} className="ielts-radio-label">
                      {option}
                    </Label>
                  </div>
                )
              })}
            </div>
          </div>
        )

      case 'SUMMARY_COMPLETION':
        return (
          <div className="space-y-4">
            <div className="ielts-question">
              {question.text.split('___').map((part, index, array) => (
                <span key={index}>
                  {part}
                  {index < array.length - 1 && (
                    <Input
                      value={answer?.[index] || ''}
                      onChange={(e) => {
                        const newAnswers = Array.isArray(answer) ? [...answer] : []
                        newAnswers[index] = e.target.value
                        onAnswerChange(question.id.toString(), newAnswers)
                      }}
                      className="inline-block w-28 h-9 ielts-input mx-1 text-center"
                      placeholder={(question.id + index).toString()}
                    />
                  )}
                </span>
              ))}
            </div>
            {question.maxWords && (
              <p className="ielts-low-contrast">
                Write NO MORE THAN {question.maxWords === 1 ? 'ONE WORD' : `${question.maxWords} WORDS`} from the text for each answer.
              </p>
            )}
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="h-screen bg-white flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-3 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="text-red-600 font-bold text-xl">IELTS</div>
          <div className="text-sm text-gray-600">Test taker ID</div>
        </div>
        <div className="flex items-center space-x-4 text-gray-600">
          <Wifi className="w-4 h-4" />
          <Bell className="w-4 h-4" />
          <Menu className="w-4 h-4" />
        </div>
      </div>

      {/* Part Header */}
      <div className="bg-gray-100 px-6 py-4 border-b border-gray-200">
        <h2 className="ielts-part-title">Part {currentPartData.partNumber}</h2>
        <p className="ielts-instructions mt-2">{currentPartData.instructions}</p>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Passage Section */}
        <div className="w-1/2 border-r border-gray-200">
          <div className="p-6">
            <h3 className="ielts-title mb-5">{currentPartData.title}</h3>
            <ScrollArea className="h-[calc(100vh-280px)]">
              <div className="ielts-passage max-w-none">
                {currentPartData.passage.split('\n\n').map((paragraph, index) => (
                  <p key={index} className="ielts-passage">
                    {paragraph}
                  </p>
                ))}
              </div>
            </ScrollArea>
          </div>
        </div>

        {/* Questions Section */}
        <div className="w-1/2">
          <div className="p-6">
            <div className="mb-5">
              <h3 className="ielts-title">Questions {currentPartData.questionRange}</h3>
              <p className="ielts-instructions mt-3">
                Choose <strong>TRUE</strong> if the statement agrees with the information given in the text, choose <strong>FALSE</strong> if
                the statement contradicts the information, or choose <strong>NOT GIVEN</strong> if there is no information on
                this.
              </p>
            </div>

            <ScrollArea className="h-[calc(100vh-320px)]">
              <div className="space-y-6">
                {currentQuestions.map((question) => (
                  <div key={question.id} className="border-b border-gray-100 pb-5 last:border-b-0">
                    <div className="flex items-start space-x-4">
                      <div className="ielts-question-number mt-1 min-w-[24px]">
                        {question.id}
                      </div>
                      <div className="flex-1">
                        {renderQuestion(question)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        </div>
      </div>

      {/* Bottom Navigation */}
      <div className="bg-white border-t border-gray-200 px-6 py-3">
        <div className="flex items-center justify-between">
          {/* Part Navigation */}
          <div className="flex items-center space-x-4">
            {parts.map((part, index) => (
              <button
                key={index}
                onClick={() => {
                  setCurrentPart(index)
                  setCurrentQuestionPage(0)
                }}
                className={`px-3 py-1 ielts-nav-button rounded ${
                  currentPart === index
                    ? 'bg-blue-100 text-blue-700 font-medium'
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                Part {part.partNumber}
              </button>
            ))}
          </div>

          {/* Question Page Navigation */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              {Array.from({ length: totalPages }, (_, i) => (
                <button
                  key={i}
                  onClick={() => setCurrentQuestionPage(i)}
                  className={`w-6 h-6 text-xs rounded ${
                    currentQuestionPage === i
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  {i + 1}
                </button>
              ))}
            </div>

            <div className="text-sm text-gray-500">
              Part {currentPartData.partNumber} &nbsp;&nbsp; {startQuestionIndex + 1} of {totalQuestions}
            </div>

            <div className="text-sm text-gray-500">
              Part 3 &nbsp;&nbsp; 0 of 14
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  if (currentQuestionPage > 0) {
                    setCurrentQuestionPage(currentQuestionPage - 1)
                  } else if (currentPart > 0) {
                    setCurrentPart(currentPart - 1)
                    setCurrentQuestionPage(Math.ceil(parts[currentPart - 1].questions.length / questionsPerPage) - 1)
                  }
                }}
                disabled={currentPart === 0 && currentQuestionPage === 0}
                className="w-8 h-8 p-0 bg-gray-600 text-white hover:bg-gray-700"
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  if (currentQuestionPage < totalPages - 1) {
                    setCurrentQuestionPage(currentQuestionPage + 1)
                  } else if (currentPart < parts.length - 1) {
                    setCurrentPart(currentPart + 1)
                    setCurrentQuestionPage(0)
                  } else {
                    onSubmit()
                  }
                }}
                className="w-8 h-8 p-0 bg-gray-600 text-white hover:bg-gray-700"
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
