"use client"

import { useState } from "react"
import { InspecraReadingInterface } from "@/components/inspera-reading-interface"
import { authenticReadingTest } from "@/data/authentic-reading-test"
import { Button } from "@/components/ui/button"

export default function DemoPage() {
  const [showTest, setShowTest] = useState(false)
  const [answers, setAnswers] = useState<Record<string, any>>({})

  const handleAnswerChange = (questionId: string, answer: any) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }))
  }

  const handleSubmit = () => {
    console.log("Test submitted with answers:", answers)
    alert("Test submitted! Check console for answers.")
  }

  if (showTest) {
    return (
      <InspecraReadingInterface
        testTakerId="12345678"
        parts={authenticReadingTest.parts}
        answers={answers}
        onAnswerChange={handleAnswerChange}
        onSubmit={handleSubmit}
      />
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-8">
      <div className="max-w-2xl mx-auto text-center">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Authentic IELTS Reading Test
          </h1>
          <p className="text-lg text-gray-600 mb-8">
            Experience the exact Inspera interface used in official IELTS test centers worldwide.
            This demo replicates every detail of the real test environment.
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">
            Test Information
          </h2>
          <div className="space-y-4 text-left">
            <div className="flex justify-between items-center py-2 border-b">
              <span className="font-medium">Duration:</span>
              <span>60 minutes</span>
            </div>
            <div className="flex justify-between items-center py-2 border-b">
              <span className="font-medium">Questions:</span>
              <span>40 questions (33 shown in demo)</span>
            </div>
            <div className="flex justify-between items-center py-2 border-b">
              <span className="font-medium">Parts:</span>
              <span>3 reading passages</span>
            </div>
            <div className="flex justify-between items-center py-2 border-b">
              <span className="font-medium">Question Types:</span>
              <span>True/False/Not Given, Multiple Choice, Fill in Blanks</span>
            </div>
            <div className="flex justify-between items-center py-2">
              <span className="font-medium">Interface:</span>
              <span className="text-red-600 font-semibold">Official Inspera Design</span>
            </div>
          </div>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <h3 className="text-lg font-semibold text-blue-900 mb-2">
            🎯 Authentic Features
          </h3>
          <ul className="text-sm text-blue-800 space-y-1 text-left">
            <li>• Exact IELTS logo and header design</li>
            <li>• Split-screen layout: passage left, questions right</li>
            <li>• Official question numbering and navigation</li>
            <li>• Authentic radio button styling</li>
            <li>• Real Cambridge IELTS content</li>
            <li>• Part-by-part navigation system</li>
            <li>• Inspera-style bottom navigation bar</li>
          </ul>
        </div>

        <Button 
          onClick={() => setShowTest(true)}
          className="bg-red-600 hover:bg-red-700 text-white px-8 py-3 text-lg font-medium"
        >
          Start Authentic Reading Test
        </Button>

        <p className="text-sm text-gray-500 mt-4">
          This interface is an exact replica of the official IELTS computer-based test system.
        </p>
      </div>
    </div>
  )
}
