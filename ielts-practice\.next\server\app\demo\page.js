/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/demo/page";
exports.ids = ["app/demo/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdemo%2Fpage&page=%2Fdemo%2Fpage&appPaths=%2Fdemo%2Fpage&pagePath=private-next-app-dir%2Fdemo%2Fpage.tsx&appDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CIELTS%20practice%5Cielts-practice%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CIELTS%20practice%5Cielts-practice&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdemo%2Fpage&page=%2Fdemo%2Fpage&appPaths=%2Fdemo%2Fpage&pagePath=private-next-app-dir%2Fdemo%2Fpage.tsx&appDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CIELTS%20practice%5Cielts-practice%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CIELTS%20practice%5Cielts-practice&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/demo/page.tsx */ \"(rsc)/./src/app/demo/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'demo',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/demo/page\",\n        pathname: \"/demo\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdemo%2Fpage&page=%2Fdemo%2Fpage&appPaths=%2Fdemo%2Fpage&pagePath=private-next-app-dir%2Fdemo%2Fpage.tsx&appDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CIELTS%20practice%5Cielts-practice%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CIELTS%20practice%5Cielts-practice&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers.tsx */ \"(rsc)/./src/components/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cdemo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cdemo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/demo/page.tsx */ \"(rsc)/./src/app/demo/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1dpbmRvd3MlMjAxMSU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNJRUxUUyUyMHByYWN0aWNlJTVDJTVDaWVsdHMtcHJhY3RpY2UlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkZW1vJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBKQUFpSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcV2luZG93cyAxMVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxJRUxUUyBwcmFjdGljZVxcXFxpZWx0cy1wcmFjdGljZVxcXFxzcmNcXFxcYXBwXFxcXGRlbW9cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cdemo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV2luZG93cyAxMVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxJRUxUUyBwcmFjdGljZVxcaWVsdHMtcHJhY3RpY2VcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/demo/page.tsx":
/*!*******************************!*\
  !*** ./src/app/demo/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\IELTS practice\\ielts-practice\\src\\app\\demo\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"da67e0c51305\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdpbmRvd3MgMTFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcSUVMVFMgcHJhY3RpY2VcXGllbHRzLXByYWN0aWNlXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJkYTY3ZTBjNTEzMDVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers */ \"(rsc)/./src/components/providers.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"IELTS Practice Platform\",\n    description: \"Practice IELTS tests with comprehensive tracking and analytics\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default().variable)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ Providers)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Providers = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\IELTS practice\\ielts-practice\\src\\components\\providers.tsx",
"Providers",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers.tsx */ \"(ssr)/./src/components/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cdemo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cdemo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/demo/page.tsx */ \"(ssr)/./src/app/demo/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1dpbmRvd3MlMjAxMSU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNJRUxUUyUyMHByYWN0aWNlJTVDJTVDaWVsdHMtcHJhY3RpY2UlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkZW1vJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBKQUFpSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcV2luZG93cyAxMVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxJRUxUUyBwcmFjdGljZVxcXFxpZWx0cy1wcmFjdGljZVxcXFxzcmNcXFxcYXBwXFxcXGRlbW9cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cdemo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/demo/page.tsx":
/*!*******************************!*\
  !*** ./src/app/demo/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DemoPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_inspera_reading_interface__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/inspera-reading-interface */ \"(ssr)/./src/components/inspera-reading-interface.tsx\");\n/* harmony import */ var _data_authentic_reading_test__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/authentic-reading-test */ \"(ssr)/./src/data/authentic-reading-test.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DemoPage() {\n    const [showTest, setShowTest] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [answers, setAnswers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const handleAnswerChange = (questionId, answer)=>{\n        setAnswers((prev)=>({\n                ...prev,\n                [questionId]: answer\n            }));\n    };\n    const handleSubmit = ()=>{\n        console.log(\"Test submitted with answers:\", answers);\n        alert(\"Test submitted! Check console for answers.\");\n    };\n    if (showTest) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inspera_reading_interface__WEBPACK_IMPORTED_MODULE_2__.InspecraReadingInterface, {\n            testTakerId: \"12345678\",\n            parts: _data_authentic_reading_test__WEBPACK_IMPORTED_MODULE_3__.authenticReadingTest.parts,\n            answers: answers,\n            onAnswerChange: handleAnswerChange,\n            onSubmit: handleSubmit\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex items-center justify-center p-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-2xl mx-auto text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Authentic IELTS Reading Test\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 mb-8\",\n                            children: \"Experience the exact Inspera interface used in official IELTS test centers worldwide. This demo replicates every detail of the real test environment.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-8 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold text-gray-900 mb-4\",\n                            children: \"Test Information\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4 text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center py-2 border-b\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Duration:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"60 minutes\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center py-2 border-b\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Questions:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"40 questions (33 shown in demo)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center py-2 border-b\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Parts:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"3 reading passages\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center py-2 border-b\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Question Types:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"True/False/Not Given, Multiple Choice, Fill in Blanks\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center py-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Interface:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-600 font-semibold\",\n                                            children: \"Official Inspera Design\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-blue-900 mb-2\",\n                            children: \"\\uD83C\\uDFAF Authentic Features\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"text-sm text-blue-800 space-y-1 text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• Exact IELTS logo and header design\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• Split-screen layout: passage left, questions right\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• Official question numbering and navigation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• Authentic radio button styling\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• Real Cambridge IELTS content\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• Part-by-part navigation system\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• Inspera-style bottom navigation bar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    onClick: ()=>setShowTest(true),\n                    className: \"bg-red-600 hover:bg-red-700 text-white px-8 py-3 text-lg font-medium\",\n                    children: \"Start Authentic Reading Test\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-500 mt-4\",\n                    children: \"This interface is an exact replica of the official IELTS computer-based test system.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/demo/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/inspera-reading-interface.tsx":
/*!******************************************************!*\
  !*** ./src/components/inspera-reading-interface.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InspecraReadingInterface: () => (/* binding */ InspecraReadingInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(ssr)/./src/components/ui/radio-group.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(ssr)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronLeft_ChevronRight_Menu_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronLeft,ChevronRight,Menu,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronLeft_ChevronRight_Menu_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronLeft,ChevronRight,Menu,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronLeft_ChevronRight_Menu_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronLeft,ChevronRight,Menu,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronLeft_ChevronRight_Menu_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronLeft,ChevronRight,Menu,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronLeft_ChevronRight_Menu_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronLeft,ChevronRight,Menu,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* __next_internal_client_entry_do_not_use__ InspecraReadingInterface auto */ \n\n\n\n\n\n\n\nfunction InspecraReadingInterface({ testTakerId, parts, answers, onAnswerChange, onSubmit }) {\n    const [currentPart, setCurrentPart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentQuestionPage, setCurrentQuestionPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const questionsPerPage = 6;\n    const currentPartData = parts[currentPart];\n    const totalQuestions = currentPartData.questions.length;\n    const totalPages = Math.ceil(totalQuestions / questionsPerPage);\n    const startQuestionIndex = currentQuestionPage * questionsPerPage;\n    const endQuestionIndex = Math.min(startQuestionIndex + questionsPerPage, totalQuestions);\n    const currentQuestions = currentPartData.questions.slice(startQuestionIndex, endQuestionIndex);\n    const renderQuestion = (question)=>{\n        const answer = answers[question.id.toString()];\n        switch(question.type){\n            case 'TRUE_FALSE_NOT_GIVEN':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"ielts-question\",\n                            children: question.text\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_3__.RadioGroup, {\n                            value: answer || '',\n                            onValueChange: (value)=>onAnswerChange(question.id.toString(), value),\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_3__.RadioGroupItem, {\n                                            value: \"TRUE\",\n                                            id: `${question.id}-true`,\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                            htmlFor: `${question.id}-true`,\n                                            className: \"ielts-radio-label\",\n                                            children: \"TRUE\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_3__.RadioGroupItem, {\n                                            value: \"FALSE\",\n                                            id: `${question.id}-false`,\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                            htmlFor: `${question.id}-false`,\n                                            className: \"ielts-radio-label\",\n                                            children: \"FALSE\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_3__.RadioGroupItem, {\n                                            value: \"NOT GIVEN\",\n                                            id: `${question.id}-not-given`,\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                            htmlFor: `${question.id}-not-given`,\n                                            className: \"ielts-radio-label\",\n                                            children: \"NOT GIVEN\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 11\n                }, this);\n            case 'MULTIPLE_CHOICE':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"ielts-question\",\n                            children: question.text\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_3__.RadioGroup, {\n                            value: answer || '',\n                            onValueChange: (value)=>onAnswerChange(question.id.toString(), value),\n                            className: \"space-y-3\",\n                            children: question.options?.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_3__.RadioGroupItem, {\n                                            value: option,\n                                            id: `${question.id}-${index}`,\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                            htmlFor: `${question.id}-${index}`,\n                                            className: \"ielts-radio-label\",\n                                            children: option\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 11\n                }, this);\n            case 'FILL_IN_BLANK':\n            case 'SHORT_ANSWER':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"ielts-question\",\n                            children: question.text\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                            value: answer || '',\n                            onChange: (e)=>onAnswerChange(question.id.toString(), e.target.value),\n                            className: \"max-w-xs h-9 ielts-input\",\n                            placeholder: \"Type your answer\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 11\n                }, this);\n            case 'SENTENCE_COMPLETION':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ielts-question\",\n                            children: question.text.split('___').map((part, index, array)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        part,\n                                        index < array.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            value: answer || '',\n                                            onChange: (e)=>onAnswerChange(question.id.toString(), e.target.value),\n                                            className: \"inline-block w-32 h-9 ielts-input mx-1 text-center\",\n                                            placeholder: question.id.toString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 13\n                        }, this),\n                        question.maxWords && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm ielts-low-contrast\",\n                            children: [\n                                \"Write NO MORE THAN \",\n                                question.maxWords === 1 ? 'ONE WORD' : `${question.maxWords} WORDS`\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 11\n                }, this);\n            case 'MATCHING_HEADINGS':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"ielts-question font-medium\",\n                            children: question.text\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-5 rounded border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"ielts-instructions font-medium mb-4\",\n                                    children: \"List of Headings\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: question.headings?.map((heading, index)=>{\n                                        const headingNumber = index + 1;\n                                        const isUsed = question.usedHeadings?.includes(headingNumber);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `ielts-question p-3 rounded ${isUsed ? 'bg-gray-200 text-gray-500' : 'bg-white border'}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium mr-3\",\n                                                    children: String.fromCharCode(97 + index)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 23\n                                                }, this),\n                                                heading\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"ielts-instructions font-medium\",\n                                    children: \"Choose the correct heading:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_3__.RadioGroup, {\n                                    value: answer || '',\n                                    onValueChange: (value)=>onAnswerChange(question.id.toString(), value),\n                                    className: \"space-y-3\",\n                                    children: question.headings?.map((heading, index)=>{\n                                        const headingLetter = String.fromCharCode(97 + index);\n                                        const isUsed = question.usedHeadings?.includes(index + 1);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `flex items-center space-x-3 ${isUsed ? 'opacity-50' : ''}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_3__.RadioGroupItem, {\n                                                    value: headingLetter,\n                                                    id: `${question.id}-${headingLetter}`,\n                                                    className: \"w-4 h-4\",\n                                                    disabled: isUsed\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: `${question.id}-${headingLetter}`,\n                                                    className: \"ielts-radio-label\",\n                                                    children: headingLetter\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 11\n                }, this);\n            case 'MULTIPLE_SELECTION':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"ielts-question font-medium\",\n                            children: question.text\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"ielts-instructions\",\n                            children: [\n                                \"Choose \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: question.selectCount === 2 ? 'TWO' : question.selectCount\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 22\n                                }, this),\n                                \" correct answers.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: question.options?.map((option, index)=>{\n                                const currentAnswers = Array.isArray(answer) ? answer : [];\n                                const isSelected = currentAnswers.includes(option);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: `${question.id}-${index}`,\n                                            checked: isSelected,\n                                            onChange: (e)=>{\n                                                let newAnswers = [\n                                                    ...currentAnswers\n                                                ];\n                                                if (e.target.checked) {\n                                                    if (newAnswers.length < (question.selectCount || 2)) {\n                                                        newAnswers.push(option);\n                                                    }\n                                                } else {\n                                                    newAnswers = newAnswers.filter((a)=>a !== option);\n                                                }\n                                                onAnswerChange(question.id.toString(), newAnswers);\n                                            },\n                                            className: \"w-4 h-4 mt-1\",\n                                            disabled: !isSelected && Array.isArray(answer) && answer.length >= (question.selectCount || 2)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                            htmlFor: `${question.id}-${index}`,\n                                            className: \"ielts-radio-label\",\n                                            children: option\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 11\n                }, this);\n            case 'SUMMARY_COMPLETION':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ielts-question\",\n                            children: question.text.split('___').map((part, index, array)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        part,\n                                        index < array.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            value: answer?.[index] || '',\n                                            onChange: (e)=>{\n                                                const newAnswers = Array.isArray(answer) ? [\n                                                    ...answer\n                                                ] : [];\n                                                newAnswers[index] = e.target.value;\n                                                onAnswerChange(question.id.toString(), newAnswers);\n                                            },\n                                            className: \"inline-block w-28 h-9 ielts-input mx-1 text-center\",\n                                            placeholder: (question.id + index).toString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 13\n                        }, this),\n                        question.maxWords && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"ielts-low-contrast\",\n                            children: [\n                                \"Write NO MORE THAN \",\n                                question.maxWords === 1 ? 'ONE WORD' : `${question.maxWords} WORDS`,\n                                \" from the text for each answer.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 11\n                }, this);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen bg-white flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-3 flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-red-600 font-bold text-xl\",\n                                children: \"IELTS\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Test taker ID\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4 text-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronLeft_ChevronRight_Menu_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronLeft_ChevronRight_Menu_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronLeft_ChevronRight_Menu_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-100 px-6 py-4 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"ielts-part-title\",\n                        children: [\n                            \"Part \",\n                            currentPartData.partNumber\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"ielts-instructions mt-2\",\n                        children: currentPartData.instructions\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-1/2 border-r border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"ielts-title mb-5\",\n                                    children: currentPartData.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                                    className: \"h-[calc(100vh-280px)]\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ielts-passage max-w-none\",\n                                        children: currentPartData.passage.split('\\n\\n').map((paragraph, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"ielts-passage\",\n                                                children: paragraph\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"ielts-title\",\n                                            children: [\n                                                \"Questions \",\n                                                currentPartData.questionRange\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ielts-instructions mt-3\",\n                                            children: [\n                                                \"Choose \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"TRUE\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 24\n                                                }, this),\n                                                \" if the statement agrees with the information given in the text, choose \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"FALSE\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 117\n                                                }, this),\n                                                \" if the statement contradicts the information, or choose \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"NOT GIVEN\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 70\n                                                }, this),\n                                                \" if there is no information on this.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                                    className: \"h-[calc(100vh-320px)]\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: currentQuestions.map((question)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-b border-gray-100 pb-5 last:border-b-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ielts-question-number mt-1 min-w-[24px]\",\n                                                            children: question.id\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: renderQuestion(question)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, question.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                lineNumber: 292,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-t border-gray-200 px-6 py-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: parts.map((part, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setCurrentPart(index);\n                                        setCurrentQuestionPage(0);\n                                    },\n                                    className: `px-3 py-1 ielts-nav-button rounded ${currentPart === index ? 'bg-blue-100 text-blue-700 font-medium' : 'text-gray-600 hover:bg-gray-100'}`,\n                                    children: [\n                                        \"Part \",\n                                        part.partNumber\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-sm text-gray-600\",\n                                    children: Array.from({\n                                        length: totalPages\n                                    }, (_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setCurrentQuestionPage(i),\n                                            className: `w-6 h-6 text-xs rounded ${currentQuestionPage === i ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                                            children: i + 1\n                                        }, i, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: [\n                                        \"Part \",\n                                        currentPartData.partNumber,\n                                        \" \\xa0\\xa0 \",\n                                        startQuestionIndex + 1,\n                                        \" of \",\n                                        totalQuestions\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"Part 3 \\xa0\\xa0 0 of 14\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>{\n                                                if (currentQuestionPage > 0) {\n                                                    setCurrentQuestionPage(currentQuestionPage - 1);\n                                                } else if (currentPart > 0) {\n                                                    setCurrentPart(currentPart - 1);\n                                                    setCurrentQuestionPage(Math.ceil(parts[currentPart - 1].questions.length / questionsPerPage) - 1);\n                                                }\n                                            },\n                                            disabled: currentPart === 0 && currentQuestionPage === 0,\n                                            className: \"w-8 h-8 p-0 bg-gray-600 text-white hover:bg-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronLeft_ChevronRight_Menu_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>{\n                                                if (currentQuestionPage < totalPages - 1) {\n                                                    setCurrentQuestionPage(currentQuestionPage + 1);\n                                                } else if (currentPart < parts.length - 1) {\n                                                    setCurrentPart(currentPart + 1);\n                                                    setCurrentQuestionPage(0);\n                                                } else {\n                                                    onSubmit();\n                                                }\n                                            },\n                                            className: \"w-8 h-8 p-0 bg-gray-600 text-white hover:bg-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronLeft_ChevronRight_Menu_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                    lineNumber: 343,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                lineNumber: 342,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n        lineNumber: 271,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/inspera-reading-interface.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\providers.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUVpRDtBQU0xQyxTQUFTQyxVQUFVLEVBQUVDLFFBQVEsRUFBa0I7SUFDcEQscUJBQ0UsOERBQUNGLDREQUFlQTtrQkFDYkU7Ozs7OztBQUdQIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdpbmRvd3MgMTFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcSUVMVFMgcHJhY3RpY2VcXGllbHRzLXByYWN0aWNlXFxzcmNcXGNvbXBvbmVudHNcXHByb3ZpZGVycy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSBcIm5leHQtYXV0aC9yZWFjdFwiXG5cbmludGVyZmFjZSBQcm92aWRlcnNQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFByb3ZpZGVycyh7IGNoaWxkcmVuIH06IFByb3ZpZGVyc1Byb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPFNlc3Npb25Qcm92aWRlcj5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L1Nlc3Npb25Qcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlNlc3Npb25Qcm92aWRlciIsIlByb3ZpZGVycyIsImNoaWxkcmVuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n            destructive: \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n            sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n            lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n            icon: \"size-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nfunction Button({ className, variant, size, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"button\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nfunction Input({ className, type, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        \"data-slot\": \"input\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\", \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/label.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/label.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-label */ \"(ssr)/./node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Label auto */ \n\n\n\nfunction Label({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        \"data-slot\": \"label\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9sYWJlbC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFOEI7QUFDeUI7QUFFdkI7QUFFaEMsU0FBU0csTUFBTSxFQUNiQyxTQUFTLEVBQ1QsR0FBR0MsT0FDOEM7SUFDakQscUJBQ0UsOERBQUNKLHVEQUFtQjtRQUNsQk0sYUFBVTtRQUNWSCxXQUFXRiw4Q0FBRUEsQ0FDWCx1TkFDQUU7UUFFRCxHQUFHQyxLQUFLOzs7Ozs7QUFHZjtBQUVnQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXaW5kb3dzIDExXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXElFTFRTIHByYWN0aWNlXFxpZWx0cy1wcmFjdGljZVxcc3JjXFxjb21wb25lbnRzXFx1aVxcbGFiZWwudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgKiBhcyBMYWJlbFByaW1pdGl2ZSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LWxhYmVsXCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5mdW5jdGlvbiBMYWJlbCh7XG4gIGNsYXNzTmFtZSxcbiAgLi4ucHJvcHNcbn06IFJlYWN0LkNvbXBvbmVudFByb3BzPHR5cGVvZiBMYWJlbFByaW1pdGl2ZS5Sb290Pikge1xuICByZXR1cm4gKFxuICAgIDxMYWJlbFByaW1pdGl2ZS5Sb290XG4gICAgICBkYXRhLXNsb3Q9XCJsYWJlbFwiXG4gICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICBcImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHRleHQtc20gbGVhZGluZy1ub25lIGZvbnQtbWVkaXVtIHNlbGVjdC1ub25lIGdyb3VwLWRhdGEtW2Rpc2FibGVkPXRydWVdOnBvaW50ZXItZXZlbnRzLW5vbmUgZ3JvdXAtZGF0YS1bZGlzYWJsZWQ9dHJ1ZV06b3BhY2l0eS01MCBwZWVyLWRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBwZWVyLWRpc2FibGVkOm9wYWNpdHktNTBcIixcbiAgICAgICAgY2xhc3NOYW1lXG4gICAgICApfVxuICAgICAgey4uLnByb3BzfVxuICAgIC8+XG4gIClcbn1cblxuZXhwb3J0IHsgTGFiZWwgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiTGFiZWxQcmltaXRpdmUiLCJjbiIsIkxhYmVsIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJSb290IiwiZGF0YS1zbG90Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/label.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/radio-group.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/radio-group.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RadioGroup: () => (/* binding */ RadioGroup),\n/* harmony export */   RadioGroupItem: () => (/* binding */ RadioGroupItem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_radio_group__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-radio-group */ \"(ssr)/./node_modules/@radix-ui/react-radio-group/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_CircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CircleIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ RadioGroup,RadioGroupItem auto */ \n\n\n\n\nfunction RadioGroup({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_radio_group__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        \"data-slot\": \"radio-group\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"grid gap-3\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\radio-group.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\nfunction RadioGroupItem({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_radio_group__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        \"data-slot\": \"radio-group-item\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-input text-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 aspect-square size-4 shrink-0 rounded-full border shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_radio_group__WEBPACK_IMPORTED_MODULE_3__.Indicator, {\n            \"data-slot\": \"radio-group-indicator\",\n            className: \"relative flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"fill-primary absolute top-1/2 left-1/2 size-2 -translate-x-1/2 -translate-y-1/2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\radio-group.tsx\",\n                lineNumber: 39,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\radio-group.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\radio-group.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/radio-group.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/scroll-area.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/scroll-area.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ScrollArea: () => (/* binding */ ScrollArea),\n/* harmony export */   ScrollBar: () => (/* binding */ ScrollBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-scroll-area */ \"(ssr)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ScrollArea,ScrollBar auto */ \n\n\n\nfunction ScrollArea({ className, children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        \"data-slot\": \"scroll-area\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Viewport, {\n                \"data-slot\": \"scroll-area-viewport\",\n                className: \"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScrollBar, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Corner, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\nfunction ScrollBar({ className, orientation = \"vertical\", ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollAreaScrollbar, {\n        \"data-slot\": \"scroll-area-scrollbar\",\n        orientation: orientation,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex touch-none p-px transition-colors select-none\", orientation === \"vertical\" && \"h-full w-2.5 border-l border-l-transparent\", orientation === \"horizontal\" && \"h-2.5 flex-col border-t border-t-transparent\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollAreaThumb, {\n            \"data-slot\": \"scroll-area-thumb\",\n            className: \"bg-border relative flex-1 rounded-full\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/scroll-area.tsx\n");

/***/ }),

/***/ "(ssr)/./src/data/authentic-reading-test.ts":
/*!********************************************!*\
  !*** ./src/data/authentic-reading-test.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticReadingTest: () => (/* binding */ authenticReadingTest)\n/* harmony export */ });\nconst authenticReadingTest = {\n    testId: \"ielts-reading-authentic-1\",\n    title: \"IELTS Academic Reading Test\",\n    duration: 60,\n    parts: [\n        {\n            partNumber: 1,\n            title: \"READING PASSAGE 1 - The life and work of Marie Curie\",\n            instructions: \"You should spend about 20 minutes on Questions 1–13, which are based on Reading Passage 1 below.\",\n            questionRange: \"1–13\",\n            passage: `Marie Curie is probably the most famous woman scientist who has ever lived. Born Maria Sklodowska in Poland in 1867, she is famous for her work on radioactivity, and was twice a winner of the Nobel Prize. With her husband, Pierre Curie, and Henri Becquerel, she was awarded the 1903 Nobel Prize for Physics, and was then sole winner of the 1911 Nobel Prize for Chemistry. She was the first woman to win a Nobel Prize.\n\nFrom childhood, Marie was remarkable for her prodigious memory, and at the age of 16 won a gold medal on completion of her secondary education. Because her father lost his savings through bad investment, she then had to take work as a teacher. From her earnings she was able to finance her sister Bronia's medical studies in Paris, on the understanding that Bronia would, in turn, later help her to get an education.\n\nIn 1891 this promise was fulfilled and Marie went to Paris and began to study at the Sorbonne (the University of Paris). She often worked far into the night and lived on little more than bread and butter and tea. She came first in the examination in the physical sciences in 1893, and in 1894 was placed second in the examination in mathematical sciences. It was not until the spring of that year that she was introduced to Pierre Curie.\n\nTheir marriage in 1895 marked the start of a partnership that was soon to achieve results of world significance. Following Henri Becquerel's discovery in 1896 of a new phenomenon, which Marie later called 'radioactivity', Marie Curie decided to find out if the radioactivity discovered in uranium was to be found in other elements. She discovered that this was true for thorium.\n\nTurning her attention to minerals, she found her interest drawn to pitchblende, a mineral whose radioactivity, superior to that of pure uranium, could be explained only by the presence in the ore of small quantities of an unknown substance of very high activity. Pierre Curie joined her in the work that she had undertaken to resolve this problem, and that led to the discovery of the new elements, polonium and radium. While Pierre Curie devoted himself chiefly to the physical study of the new radiations, Marie Curie struggled to obtain pure radium in the metallic state, an undertaking no one else had previously attempted. This was achieved with the help of the chemist Andre-Louis Debierne, one of Pierre Curie's pupils. Based on the results of this research, Marie Curie received her Doctorate of Science, and in 1903 Marie and Pierre shared with Becquerel the Nobel Prize for Physics for the discovery of radioactivity.\n\nThe births of Marie's two daughters, Irène and Eve, in 1897 and 1904 failed to interrupt her scientific work. She was appointed lecturer in physics at the École Normale Supérieure for girls in Sèvres, France (1900), and introduced a method of teaching based on experimental demonstrations. In December 1904 she was appointed chief assistant in the laboratory directed by Pierre Curie.\n\nThe sudden death of her husband in 1906 was a bitter blow to Marie Curie, but was also a turning point in her career: henceforth she was to devote all her energy to completing alone the scientific work that they had undertaken. On May 13, 1906, she was appointed to the professorship that had been left vacant on her husband's death, becoming the first woman to teach at the Sorbonne. In 1911 she was awarded the Nobel Prize for Chemistry for the isolation of a pure form of radium.\n\nDuring World War I, Marie Curie, with the help of her daughter Irène, devoted herself to the development of the use of X-radiography, including the mobile units which came to be known as 'Little Curies', used for the treatment of wounded soldiers. In 1918 the Radium Institute, whose staff Irène had joined, began to operate in earnest, and became a centre for nuclear physics and chemistry. Marie Curie, now at the highest point of her fame and, from 1922, a member of the Academy of Medicine, researched the chemistry of radioactive substances and their medical applications.\n\nIn 1921, accompanied by her two daughters, Marie Curie made a triumphant journey to the United States to raise funds for research on radium. Women there presented her with a gram of radium for her campaign. Marie also gave lectures in Belgium, Brazil, Spain and Czechoslovakia and, in addition, had the satisfaction of seeing the development of the Curie Foundation in Paris, and the inauguration in 1932 in Warsaw of the Radium Institute, where her sister Bronia became director.\n\nOne of Marie Curie's outstanding achievements was to have understood the need to accumulate intense radioactive sources, not only to treat illness but also to maintain an abundant supply for research. The existence in Paris at the Radium Institute of a stock of 1.5 grams of radium made a decisive contribution to the success of the experiments undertaken in the years around 1930. This work prepared the way for the discovery of the neutron by Sir James Chadwick and, above all, for the discovery in 1934 by Irène and Frédéric Joliot-Curie of artificial radioactivity. A few months after this discovery, Marie Curie died as a result of leukaemia caused by exposure to radiation. She had often carried test tubes containing radioactive isotopes in her pocket, remarking on the pretty blue-green light they gave off.\n\nHer contribution to physics had been immense, not only in her own work, the importance of which had been demonstrated by her two Nobel Prizes, but because of her influence on subsequent generations of nuclear physicists and chemists.`,\n            questions: [\n                {\n                    id: 1,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Marie Curie's husband was a joint winner of both Marie's Nobel Prizes.\",\n                    correctAnswer: \"FALSE\",\n                    explanation: \"Pierre was joint winner of the 1903 Nobel Prize for Physics, but Marie was 'sole winner of the 1911 Nobel Prize for Chemistry'.\"\n                },\n                {\n                    id: 2,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Marie became interested in science when she was a child.\",\n                    correctAnswer: \"NOT GIVEN\",\n                    explanation: \"The passage mentions her 'prodigious memory' from childhood but doesn't specify when she became interested in science.\"\n                },\n                {\n                    id: 3,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Marie was able to attend the Sorbonne because of her sister's financial contribution.\",\n                    correctAnswer: \"TRUE\",\n                    explanation: \"Marie financed her sister Bronia's studies 'on the understanding that Bronia would, in turn, later help her to get an education. In 1891 this promise was fulfilled'.\"\n                },\n                {\n                    id: 4,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Marie stopped doing research for several years when her children were born.\",\n                    correctAnswer: \"FALSE\",\n                    explanation: \"The passage states 'The births of Marie's two daughters, Irène and Eve, in 1897 and 1904 failed to interrupt her scientific work'.\"\n                },\n                {\n                    id: 5,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Marie took over the teaching position her husband had held.\",\n                    correctAnswer: \"TRUE\",\n                    explanation: \"After Pierre's death, 'she was appointed to the professorship that had been left vacant on her husband's death'.\"\n                },\n                {\n                    id: 6,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Marie's sister Bronia studied the medical uses of radioactivity.\",\n                    correctAnswer: \"NOT GIVEN\",\n                    explanation: \"The passage mentions Bronia's medical studies and that she became director of the Radium Institute, but doesn't specify she studied medical uses of radioactivity.\"\n                },\n                {\n                    id: 7,\n                    type: 'SENTENCE_COMPLETION',\n                    text: \"When uranium was discovered to be radioactive, Marie Curie found that the element called ___ had the same property.\",\n                    maxWords: 1,\n                    correctAnswer: \"thorium\",\n                    explanation: \"Found in paragraph 4: 'She discovered that this was true for thorium'.\"\n                },\n                {\n                    id: 8,\n                    type: 'SENTENCE_COMPLETION',\n                    text: \"Marie and Pierre Curie's research into the radioactivity of the mineral known as ___ led to the discovery of two new elements.\",\n                    maxWords: 1,\n                    correctAnswer: \"pitchblende\",\n                    explanation: \"Found in paragraph 5: 'she found her interest drawn to pitchblende, a mineral whose radioactivity... led to the discovery of the new elements, polonium and radium'.\"\n                },\n                {\n                    id: 9,\n                    type: 'SENTENCE_COMPLETION',\n                    text: \"In 1911, Marie Curie received recognition for her work on the element ___.\",\n                    maxWords: 1,\n                    correctAnswer: \"radium\",\n                    explanation: \"Found in paragraph 7: 'In 1911 she was awarded the Nobel Prize for Chemistry for the isolation of a pure form of radium'.\"\n                },\n                {\n                    id: 10,\n                    type: 'SENTENCE_COMPLETION',\n                    text: \"Marie and Irène Curie developed X-radiography which was used as a medical technique for ___.\",\n                    maxWords: 1,\n                    correctAnswer: \"soldiers\",\n                    explanation: \"Found in paragraph 8: 'devoted herself to the development of the use of X-radiography... used for the treatment of wounded soldiers'.\"\n                },\n                {\n                    id: 11,\n                    type: 'SENTENCE_COMPLETION',\n                    text: \"Marie Curie saw the importance of collecting radioactive material both for research and for cases of ___.\",\n                    maxWords: 1,\n                    correctAnswer: \"illness\",\n                    explanation: \"Found in paragraph 10: 'understood the need to accumulate intense radioactive sources, not only to treat illness but also to maintain an abundant supply for research'.\"\n                },\n                {\n                    id: 12,\n                    type: 'SENTENCE_COMPLETION',\n                    text: \"The radioactive material stocked in Paris contributed to the discoveries in the 1930s of the ___ and of what was known as artificial radioactivity.\",\n                    maxWords: 1,\n                    correctAnswer: \"neutron\",\n                    explanation: \"Found in paragraph 10: 'This work prepared the way for the discovery of the neutron by Sir James Chadwick'.\"\n                },\n                {\n                    id: 13,\n                    type: 'SENTENCE_COMPLETION',\n                    text: \"During her research, Marie Curie was exposed to radiation and as a result she suffered from ___.\",\n                    maxWords: 1,\n                    correctAnswer: \"leukaemia\",\n                    explanation: \"Found in paragraph 10: 'Marie Curie died as a result of leukaemia caused by exposure to radiation'.\"\n                }\n            ]\n        },\n        {\n            partNumber: 2,\n            title: \"READING PASSAGE 2 - Young children's sense of identity\",\n            instructions: \"You should spend about 20 minutes on Questions 14–26 which are based on Reading Passage 2 below.\",\n            questionRange: \"14–26\",\n            passage: `A. A sense of self develops in young children by degrees. The process can usefully be thought of in terms of the gradual emergence of two somewhat separate features: the self as a subject, and the self as an object. William James introduced the distinction in 1892, and contemporaries of his, such as Charles Cooley, added to the developing debate. Ever since then psychologists have continued building on the theory.\n\nB. According to James, a child's first step on the road to self-understanding can be seen as the recognition that he or she exists. This is an aspect of the self that he labelled 'self-as-subject', and he gave it various elements. These included an awareness of one's own agency (i.e. one's power to act), and an awareness of one's distinctiveness from other people. These features gradually emerge as infants explore their world and interact with caregivers. Cooley (1902) suggested that a sense of the self-as-subject was primarily concerned with being able to exercise power. He proposed that the earliest examples of this are an infant's attempts to control physical objects, such as toys or his or her own limbs. This is followed by attempts to affect the behaviour of other people. For example, infants learn that when they cry or smile someone responds to them.\n\nC. Another powerful source of information for infants about the effects they can have on the world around them is provided when others mimic them. Many parents spend a lot of time, particularly in the early months, copying their infant's vocalizations and expressions. In addition, young children enjoy looking in mirrors, where the movements they can see are dependent upon their own movements. This is not to say that infants recognize the reflection as their own image (a later development). However, Lewis and Brooks-Gunn (1979) suggest that infants' developing understanding that the movements they see in the mirror are contingent on their own, leads to a growing awareness that they are distinct from other people. This is because they, and only they, can change the reflection in the mirror.\n\nD. This understanding that children gain of themselves as active agents continues to develop in their attempts to co-operate with others in play. Dunn (1988) points out that it is in such day-to-day relationships and interactions that the child's understanding of his- or herself emerges. Empirical investigations of the self-as-subject in young children are, however, rather scarce because of difficulties of communication: even if they can talk, young infants cannot verbalize very easily what they know about themselves.\n\nE. Once children have acquired a certain level of self-awareness, they begin to place themselves in a whole series of categories, which together play such an important part in defining them uniquely as 'themselves'. This second step in the development of a full sense of self is what James called the 'self-as-object'. This has been seen by many to be the aspect of the self which is most influenced by social elements, since it is made up of social roles (such as student, brother, colleague) and characteristics which derive their meaning from comparison or interaction with other people (such as trustworthiness, shyness, sporting ability).\n\nF. Cooley and other researchers suggested a close connection between a person's own understanding of their identity and other people's understanding of it. Cooley believed that people build up their sense of identity from the reactions of others to them, and from the view they believe others have of them. He called the self-as-object the 'looking-glass self', since people come to see themselves as they are reflected in others. Mead (1934) went even further, and saw the self and the social world as inextricably bound together: 'The self is essentially a social structure, and it arises in social experience ... it is impossible to conceive of a self arising outside of social experience.'\n\nG. Lewis and Brooks-Gunn argued that an important developmental milestone is reached when children become able to recognize themselves visually without the support of seeing contingent movement. This recognition occurs around their second birthday. In one experiment, Lewis and Brooks-Gunn (1979) dabbed some red powder on the noses of children who were playing in front of a mirror, and then observed how often they touched their noses. The psychologists reasoned that if the children knew what they usually looked like, they would be surprised by the unusual red mark and would start touching it. On the other hand, they found that children of 15 to 18 months are generally not able to recognize themselves unless other cues such as movement are present.\n\nH. Finally, perhaps the most graphic expressions of self-awareness in general can be seen in the displays of rage which are most common from 18 months to 3 years of age. In a longitudinal study of groups of three or four children, Bronson (1975) found that the intensity of the frustration and anger in their disagreements increased sharply between the ages of 1 and 2 years. Often, the children's disagreements involved a struggle over a toy that none of them had played with before or after the tug-of-war: the children seemed to be disputing ownership rather than wanting to play with it. Although it may be less marked in other societies, the link between the sense of 'self' and of 'ownership' is a notable feature of childhood in Western societies.`,\n            questions: [\n                {\n                    id: 14,\n                    type: 'MATCHING_HEADINGS',\n                    text: \"an account of the method used by researchers in a particular study\",\n                    correctAnswer: \"G\",\n                    explanation: \"Paragraph G describes Lewis and Brooks-Gunn's experiment with red powder on children's noses.\"\n                },\n                {\n                    id: 15,\n                    type: 'MATCHING_HEADINGS',\n                    text: \"the role of imitation in developing a sense of identity\",\n                    correctAnswer: \"C\",\n                    explanation: \"Paragraph C discusses how parents mimic infants and how this helps develop self-awareness.\"\n                },\n                {\n                    id: 16,\n                    type: 'MATCHING_HEADINGS',\n                    text: \"the age at which children can usually identify a static image of themselves\",\n                    correctAnswer: \"G\",\n                    explanation: \"Paragraph G states 'This recognition occurs around their second birthday'.\"\n                },\n                {\n                    id: 17,\n                    type: 'MATCHING_HEADINGS',\n                    text: \"a reason for the limitations of scientific research into 'self-as-subject'\",\n                    correctAnswer: \"D\",\n                    explanation: \"Paragraph D mentions 'difficulties of communication: even if they can talk, young infants cannot verbalize very easily what they know about themselves'.\"\n                },\n                {\n                    id: 18,\n                    type: 'MATCHING_HEADINGS',\n                    text: \"reference to a possible link between culture and a particular form of behaviour\",\n                    correctAnswer: \"H\",\n                    explanation: \"Paragraph H states 'Although it may be less marked in other societies, the link between the sense of 'self' and of 'ownership' is a notable feature of childhood in Western societies'.\"\n                },\n                {\n                    id: 19,\n                    type: 'MATCHING_HEADINGS',\n                    text: \"examples of the wide range of features that contribute to the sense of 'self-as-object'\",\n                    correctAnswer: \"E\",\n                    explanation: \"Paragraph E lists 'social roles (such as student, brother, colleague) and characteristics which derive their meaning from comparison or interaction with other people (such as trustworthiness, shyness, sporting ability)'.\"\n                },\n                {\n                    id: 21,\n                    type: 'MATCHING_HEADINGS',\n                    text: \"Choose the correct heading for section A and move it into the gap.\",\n                    headings: [\n                        \"How a concept from one field of study was applied in another\",\n                        \"A lack of investment in driver training\",\n                        \"Areas of doubt and disagreement between experts\",\n                        \"How different countries have dealt with traffic congestion\",\n                        \"The impact of driver behavior on traffic speed\",\n                        \"A proposal to take control away from the driver\"\n                    ],\n                    usedHeadings: [\n                        2,\n                        4,\n                        5,\n                        6\n                    ] // These headings are already used\n                },\n                {\n                    id: 22,\n                    type: 'MULTIPLE_SELECTION',\n                    text: \"Which TWO statements reflect civil engineers' opinions of the physicists' theories?\",\n                    selectCount: 2,\n                    options: [\n                        \"They fail to take into account road maintenance.\",\n                        \"They may have little to do with everyday traffic behaviour.\",\n                        \"They are inconsistent with chaos theory.\",\n                        \"They do not really describe anything new.\",\n                        \"They can easily be disproved.\"\n                    ]\n                }\n            ]\n        },\n        {\n            partNumber: 3,\n            title: \"The Impact of Social Media on Modern Communication\",\n            instructions: \"Read the text and answer questions 27–40.\",\n            questionRange: \"27–33\",\n            passage: `Social media has fundamentally transformed the way humans communicate, creating unprecedented opportunities for connection while simultaneously raising concerns about the quality and authenticity of modern interactions. The rise of platforms such as Facebook, Twitter, Instagram, and TikTok has created a global communication network that operates 24 hours a day, seven days a week.\n\nOne of the most significant impacts of social media has been the democratization of information sharing. Previously, the dissemination of news and information was controlled by traditional media outlets such as newspapers, television, and radio stations. Today, any individual with internet access can share information instantly with a global audience. This has led to both positive and negative consequences. On the positive side, social movements have been able to organize more effectively, marginalized voices have found platforms to express themselves, and breaking news can be shared in real-time. However, this democratization has also led to the spread of misinformation and the creation of echo chambers where people are exposed only to information that confirms their existing beliefs.\n\nThe psychological impact of social media use has become a subject of intense research and debate. Studies have shown that excessive use of social media can lead to increased feelings of anxiety, depression, and social isolation, particularly among young people. The constant comparison with others' curated online personas can create unrealistic expectations and feelings of inadequacy. Furthermore, the addictive nature of social media platforms, designed to maximize user engagement through intermittent reinforcement schedules, has raised concerns about digital wellness and the need for better regulation of these technologies.\n\nDespite these concerns, social media has also created new opportunities for education, business, and creative expression. Online learning platforms have made education more accessible, small businesses can reach global markets through social media marketing, and artists and creators can build audiences without traditional gatekeepers. The COVID-19 pandemic highlighted the importance of digital communication tools in maintaining social connections during periods of physical isolation.\n\nLooking forward, the challenge will be to harness the benefits of social media while mitigating its negative effects. This will likely require a combination of technological solutions, regulatory frameworks, and digital literacy education to help users navigate the complex landscape of modern digital communication.`,\n            questions: [\n                {\n                    id: 27,\n                    type: 'MULTIPLE_CHOICE',\n                    text: \"According to the passage, social media has democratized information sharing by:\",\n                    options: [\n                        \"replacing traditional media outlets entirely\",\n                        \"allowing anyone with internet access to share information globally\",\n                        \"improving the quality of news reporting\",\n                        \"reducing the cost of information distribution\"\n                    ]\n                },\n                {\n                    id: 28,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Echo chambers are created when people only see information that supports their existing beliefs.\"\n                },\n                {\n                    id: 29,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Young people are more affected by social media's psychological impact than adults.\"\n                },\n                {\n                    id: 30,\n                    type: 'FILL_IN_BLANK',\n                    text: \"Social media platforms use _______ reinforcement schedules to maximize user engagement.\"\n                },\n                {\n                    id: 31,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"The COVID-19 pandemic proved that digital communication is superior to face-to-face interaction.\"\n                },\n                {\n                    id: 32,\n                    type: 'MULTIPLE_CHOICE',\n                    text: \"The passage suggests that addressing social media's negative effects will require:\",\n                    options: [\n                        \"banning social media platforms entirely\",\n                        \"only technological solutions\",\n                        \"a combination of technology, regulation, and education\",\n                        \"returning to traditional media only\"\n                    ]\n                },\n                {\n                    id: 33,\n                    type: 'SHORT_ANSWER',\n                    text: \"Name two positive outcomes of social media's democratization of information sharing mentioned in the passage.\"\n                },\n                {\n                    id: 34,\n                    type: 'SUMMARY_COMPLETION',\n                    text: \"For businesses, the use of complex language can have financial implications. The benefits of plain language can be seen in the case of companies who remove ___ from their forms and achieve ___ as a result.\\n\\nConsumers often complain that they experience a feeling of ___ when trying to put together do-it-yourself products which have not been tested by companies on a ___. In situations where not keeping to the correct procedures could affect safety issues, it is especially important that ___ information is not left out and no assumptions are made about a stage being self-evident or the consumer having a certain amount of ___.\\n\\nLawyers, however, have raised objections to the use of plain English. They feel that it would result in ambiguity in documents and cause people to lose faith in ___, as it would mean departing from language that has been used in the courts for a very long time.\",\n                    maxWords: 2\n                }\n            ]\n        }\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/data/authentic-reading-test.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXaW5kb3dzIDExXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXElFTFRTIHByYWN0aWNlXFxpZWx0cy1wcmFjdGljZVxcc3JjXFxsaWJcXHV0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@babel","vendor-chunks/@radix-ui","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/next-auth","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/class-variance-authority"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdemo%2Fpage&page=%2Fdemo%2Fpage&appPaths=%2Fdemo%2Fpage&pagePath=private-next-app-dir%2Fdemo%2Fpage.tsx&appDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CIELTS%20practice%5Cielts-practice%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CIELTS%20practice%5Cielts-practice&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();