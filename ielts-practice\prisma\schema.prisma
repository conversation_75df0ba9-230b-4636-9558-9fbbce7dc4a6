// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model for authentication and role management
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  password  String?
  role      UserRole @default(STUDENT)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  accounts    Account[]
  sessions    Session[]
  submissions TestSubmission[]
  progress    UserProgress[]
  createdTests Test[] @relation("TestCreator")

  @@map("users")
}

// NextAuth.js required models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verificationtokens")
}

// Test and Question models
model Test {
  id          String     @id @default(cuid())
  title       String
  description String?
  type        TestType
  difficulty  Difficulty @default(INTERMEDIATE)
  duration    Int        // Duration in minutes
  isActive    Boolean    @default(true)
  isPublished Boolean    @default(false)
  createdBy   String
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // Relations
  creator     User             @relation("TestCreator", fields: [createdBy], references: [id])
  parts       TestPart[]
  questions   Question[]       // Keep for backward compatibility
  submissions TestSubmission[]

  @@map("tests")
}

// Test parts for organizing questions (e.g., Reading Part 1, Part 2, Part 3)
model TestPart {
  id           String @id @default(cuid())
  testId       String
  partNumber   Int
  title        String
  instructions String?
  passage      String? @db.Text // For reading tests
  audioUrl     String? // For listening tests
  questionRange String? // e.g., "1-13"
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  test      Test       @relation(fields: [testId], references: [id], onDelete: Cascade)
  questions Question[]

  @@unique([testId, partNumber])
  @@map("test_parts")
}

model Question {
  id            String       @id @default(cuid())
  testId        String
  testPartId    String?      // Optional: for questions organized in parts
  type          QuestionType
  content       String       @db.Text
  options       Json?        // For multiple choice questions
  correctAnswer Json         // Flexible to handle different answer types
  points        Int          @default(1)
  order         Int
  maxWords      Int?         // For sentence completion, summary completion
  selectCount   Int?         // For multiple selection (choose 2/3 answers)
  headings      Json?        // For matching headings questions
  usedHeadings  Json?        // Track which headings are already used
  audioUrl      String?      // For listening questions
  imageUrl      String?      // For visual questions
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt

  // Relations
  test     Test              @relation(fields: [testId], references: [id], onDelete: Cascade)
  testPart TestPart?         @relation(fields: [testPartId], references: [id], onDelete: Cascade)
  answers  SubmissionAnswer[]

  @@map("questions")
}

// Submission models
model TestSubmission {
  id          String           @id @default(cuid())
  userId      String
  testId      String
  startedAt   DateTime         @default(now())
  completedAt DateTime?
  submittedAt DateTime?
  score       Float?
  totalPoints Int?
  maxPoints   Int?
  timeSpent   Int?             // Time spent in minutes
  isAutoSaved Boolean          @default(false)
  status      SubmissionStatus @default(IN_PROGRESS)
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt

  // Relations
  user    User               @relation(fields: [userId], references: [id])
  test    Test               @relation(fields: [testId], references: [id])
  answers SubmissionAnswer[]

  @@map("test_submissions")
}

model SubmissionAnswer {
  id           String @id @default(cuid())
  submissionId String
  questionId   String
  answer       Json   // Flexible to handle different answer types
  isCorrect    Boolean?
  points       Float?

  // Relations
  submission TestSubmission @relation(fields: [submissionId], references: [id], onDelete: Cascade)
  question   Question       @relation(fields: [questionId], references: [id])

  @@unique([submissionId, questionId])
  @@map("submission_answers")
}

// Progress tracking
model UserProgress {
  id           String   @id @default(cuid())
  userId       String
  testType     TestType
  totalTests   Int      @default(0)
  completedTests Int    @default(0)
  averageScore Float?
  bestScore    Float?
  lastTestDate DateTime?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id])

  @@unique([userId, testType])
  @@map("user_progress")
}

// Enums
enum UserRole {
  STUDENT
  TEACHER
  ADMIN
}

enum TestType {
  WRITING
  READING
  LISTENING
  SPEAKING
}

enum QuestionType {
  MULTIPLE_CHOICE
  TRUE_FALSE
  TRUE_FALSE_NOT_GIVEN
  FILL_IN_BLANK
  ESSAY
  SHORT_ANSWER
  SENTENCE_COMPLETION
  MATCHING_HEADINGS
  MULTIPLE_SELECTION
  SUMMARY_COMPLETION
  MATCHING
  ORDERING
}

enum Difficulty {
  BEGINNER
  INTERMEDIATE
  ADVANCED
}

enum SubmissionStatus {
  IN_PROGRESS
  COMPLETED
  ABANDONED
}
