"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/demo/page",{

/***/ "(app-pages-browser)/./src/data/authentic-reading-test.ts":
/*!********************************************!*\
  !*** ./src/data/authentic-reading-test.ts ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticReadingTest: () => (/* binding */ authenticReadingTest)\n/* harmony export */ });\nconst authenticReadingTest = {\n    testId: \"ielts-reading-authentic-1\",\n    title: \"IELTS Academic Reading Test\",\n    duration: 60,\n    parts: [\n        {\n            partNumber: 1,\n            title: \"READING PASSAGE 1 - The life and work of Marie Curie\",\n            instructions: \"You should spend about 20 minutes on Questions 1–13, which are based on Reading Passage 1 below.\",\n            questionRange: \"1–13\",\n            passage: \"Marie Curie is probably the most famous woman scientist who has ever lived. Born Maria Sklodowska in Poland in 1867, she is famous for her work on radioactivity, and was twice a winner of the Nobel Prize. With her husband, Pierre Curie, and Henri Becquerel, she was awarded the 1903 Nobel Prize for Physics, and was then sole winner of the 1911 Nobel Prize for Chemistry. She was the first woman to win a Nobel Prize.\\n\\nFrom childhood, Marie was remarkable for her prodigious memory, and at the age of 16 won a gold medal on completion of her secondary education. Because her father lost his savings through bad investment, she then had to take work as a teacher. From her earnings she was able to finance her sister Bronia's medical studies in Paris, on the understanding that Bronia would, in turn, later help her to get an education.\\n\\nIn 1891 this promise was fulfilled and Marie went to Paris and began to study at the Sorbonne (the University of Paris). She often worked far into the night and lived on little more than bread and butter and tea. She came first in the examination in the physical sciences in 1893, and in 1894 was placed second in the examination in mathematical sciences. It was not until the spring of that year that she was introduced to Pierre Curie.\\n\\nTheir marriage in 1895 marked the start of a partnership that was soon to achieve results of world significance. Following Henri Becquerel's discovery in 1896 of a new phenomenon, which Marie later called 'radioactivity', Marie Curie decided to find out if the radioactivity discovered in uranium was to be found in other elements. She discovered that this was true for thorium.\\n\\nTurning her attention to minerals, she found her interest drawn to pitchblende, a mineral whose radioactivity, superior to that of pure uranium, could be explained only by the presence in the ore of small quantities of an unknown substance of very high activity. Pierre Curie joined her in the work that she had undertaken to resolve this problem, and that led to the discovery of the new elements, polonium and radium. While Pierre Curie devoted himself chiefly to the physical study of the new radiations, Marie Curie struggled to obtain pure radium in the metallic state, an undertaking no one else had previously attempted. This was achieved with the help of the chemist Andre-Louis Debierne, one of Pierre Curie's pupils. Based on the results of this research, Marie Curie received her Doctorate of Science, and in 1903 Marie and Pierre shared with Becquerel the Nobel Prize for Physics for the discovery of radioactivity.\\n\\nThe births of Marie's two daughters, Ir\\xe8ne and Eve, in 1897 and 1904 failed to interrupt her scientific work. She was appointed lecturer in physics at the \\xc9cole Normale Sup\\xe9rieure for girls in S\\xe8vres, France (1900), and introduced a method of teaching based on experimental demonstrations. In December 1904 she was appointed chief assistant in the laboratory directed by Pierre Curie.\\n\\nThe sudden death of her husband in 1906 was a bitter blow to Marie Curie, but was also a turning point in her career: henceforth she was to devote all her energy to completing alone the scientific work that they had undertaken. On May 13, 1906, she was appointed to the professorship that had been left vacant on her husband's death, becoming the first woman to teach at the Sorbonne. In 1911 she was awarded the Nobel Prize for Chemistry for the isolation of a pure form of radium.\\n\\nDuring World War I, Marie Curie, with the help of her daughter Ir\\xe8ne, devoted herself to the development of the use of X-radiography, including the mobile units which came to be known as 'Little Curies', used for the treatment of wounded soldiers. In 1918 the Radium Institute, whose staff Ir\\xe8ne had joined, began to operate in earnest, and became a centre for nuclear physics and chemistry. Marie Curie, now at the highest point of her fame and, from 1922, a member of the Academy of Medicine, researched the chemistry of radioactive substances and their medical applications.\\n\\nIn 1921, accompanied by her two daughters, Marie Curie made a triumphant journey to the United States to raise funds for research on radium. Women there presented her with a gram of radium for her campaign. Marie also gave lectures in Belgium, Brazil, Spain and Czechoslovakia and, in addition, had the satisfaction of seeing the development of the Curie Foundation in Paris, and the inauguration in 1932 in Warsaw of the Radium Institute, where her sister Bronia became director.\\n\\nOne of Marie Curie's outstanding achievements was to have understood the need to accumulate intense radioactive sources, not only to treat illness but also to maintain an abundant supply for research. The existence in Paris at the Radium Institute of a stock of 1.5 grams of radium made a decisive contribution to the success of the experiments undertaken in the years around 1930. This work prepared the way for the discovery of the neutron by Sir James Chadwick and, above all, for the discovery in 1934 by Ir\\xe8ne and Fr\\xe9d\\xe9ric Joliot-Curie of artificial radioactivity. A few months after this discovery, Marie Curie died as a result of leukaemia caused by exposure to radiation. She had often carried test tubes containing radioactive isotopes in her pocket, remarking on the pretty blue-green light they gave off.\\n\\nHer contribution to physics had been immense, not only in her own work, the importance of which had been demonstrated by her two Nobel Prizes, but because of her influence on subsequent generations of nuclear physicists and chemists.\",\n            questions: [\n                {\n                    id: 1,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Marie Curie's husband was a joint winner of both Marie's Nobel Prizes.\",\n                    correctAnswer: \"FALSE\",\n                    explanation: \"Pierre was joint winner of the 1903 Nobel Prize for Physics, but Marie was 'sole winner of the 1911 Nobel Prize for Chemistry'.\"\n                },\n                {\n                    id: 2,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Marie became interested in science when she was a child.\",\n                    correctAnswer: \"NOT GIVEN\",\n                    explanation: \"The passage mentions her 'prodigious memory' from childhood but doesn't specify when she became interested in science.\"\n                },\n                {\n                    id: 3,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Marie was able to attend the Sorbonne because of her sister's financial contribution.\",\n                    correctAnswer: \"TRUE\",\n                    explanation: \"Marie financed her sister Bronia's studies 'on the understanding that Bronia would, in turn, later help her to get an education. In 1891 this promise was fulfilled'.\"\n                },\n                {\n                    id: 4,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Marie stopped doing research for several years when her children were born.\",\n                    correctAnswer: \"FALSE\",\n                    explanation: \"The passage states 'The births of Marie's two daughters, Irène and Eve, in 1897 and 1904 failed to interrupt her scientific work'.\"\n                },\n                {\n                    id: 5,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Marie took over the teaching position her husband had held.\",\n                    correctAnswer: \"TRUE\",\n                    explanation: \"After Pierre's death, 'she was appointed to the professorship that had been left vacant on her husband's death'.\"\n                },\n                {\n                    id: 6,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Marie's sister Bronia studied the medical uses of radioactivity.\",\n                    correctAnswer: \"NOT GIVEN\",\n                    explanation: \"The passage mentions Bronia's medical studies and that she became director of the Radium Institute, but doesn't specify she studied medical uses of radioactivity.\"\n                },\n                {\n                    id: 7,\n                    type: 'SENTENCE_COMPLETION',\n                    text: \"When uranium was discovered to be radioactive, Marie Curie found that the element called ___ had the same property.\",\n                    maxWords: 1,\n                    correctAnswer: \"thorium\",\n                    explanation: \"Found in paragraph 4: 'She discovered that this was true for thorium'.\"\n                },\n                {\n                    id: 8,\n                    type: 'SENTENCE_COMPLETION',\n                    text: \"Marie and Pierre Curie's research into the radioactivity of the mineral known as ___ led to the discovery of two new elements.\",\n                    maxWords: 1,\n                    correctAnswer: \"pitchblende\",\n                    explanation: \"Found in paragraph 5: 'she found her interest drawn to pitchblende, a mineral whose radioactivity... led to the discovery of the new elements, polonium and radium'.\"\n                },\n                {\n                    id: 9,\n                    type: 'SENTENCE_COMPLETION',\n                    text: \"In 1911, Marie Curie received recognition for her work on the element ___.\",\n                    maxWords: 1,\n                    correctAnswer: \"radium\",\n                    explanation: \"Found in paragraph 7: 'In 1911 she was awarded the Nobel Prize for Chemistry for the isolation of a pure form of radium'.\"\n                },\n                {\n                    id: 10,\n                    type: 'SENTENCE_COMPLETION',\n                    text: \"Marie and Irène Curie developed X-radiography which was used as a medical technique for ___.\",\n                    maxWords: 1,\n                    correctAnswer: \"soldiers\",\n                    explanation: \"Found in paragraph 8: 'devoted herself to the development of the use of X-radiography... used for the treatment of wounded soldiers'.\"\n                },\n                {\n                    id: 11,\n                    type: 'SENTENCE_COMPLETION',\n                    text: \"Marie Curie saw the importance of collecting radioactive material both for research and for cases of ___.\",\n                    maxWords: 1,\n                    correctAnswer: \"illness\",\n                    explanation: \"Found in paragraph 10: 'understood the need to accumulate intense radioactive sources, not only to treat illness but also to maintain an abundant supply for research'.\"\n                },\n                {\n                    id: 12,\n                    type: 'SENTENCE_COMPLETION',\n                    text: \"The radioactive material stocked in Paris contributed to the discoveries in the 1930s of the ___ and of what was known as artificial radioactivity.\",\n                    maxWords: 1,\n                    correctAnswer: \"neutron\",\n                    explanation: \"Found in paragraph 10: 'This work prepared the way for the discovery of the neutron by Sir James Chadwick'.\"\n                },\n                {\n                    id: 13,\n                    type: 'SENTENCE_COMPLETION',\n                    text: \"During her research, Marie Curie was exposed to radiation and as a result she suffered from ___.\",\n                    maxWords: 1,\n                    correctAnswer: \"leukaemia\",\n                    explanation: \"Found in paragraph 10: 'Marie Curie died as a result of leukaemia caused by exposure to radiation'.\"\n                }\n            ]\n        },\n        {\n            partNumber: 2,\n            title: \"READING PASSAGE 2 - Young children's sense of identity\",\n            instructions: \"You should spend about 20 minutes on Questions 14–26 which are based on Reading Passage 2 below.\",\n            questionRange: \"14–26\",\n            passage: \"A. A sense of self develops in young children by degrees. The process can usefully be thought of in terms of the gradual emergence of two somewhat separate features: the self as a subject, and the self as an object. William James introduced the distinction in 1892, and contemporaries of his, such as Charles Cooley, added to the developing debate. Ever since then psychologists have continued building on the theory.\\n\\nB. According to James, a child's first step on the road to self-understanding can be seen as the recognition that he or she exists. This is an aspect of the self that he labelled 'self-as-subject', and he gave it various elements. These included an awareness of one's own agency (i.e. one's power to act), and an awareness of one's distinctiveness from other people. These features gradually emerge as infants explore their world and interact with caregivers. Cooley (1902) suggested that a sense of the self-as-subject was primarily concerned with being able to exercise power. He proposed that the earliest examples of this are an infant's attempts to control physical objects, such as toys or his or her own limbs. This is followed by attempts to affect the behaviour of other people. For example, infants learn that when they cry or smile someone responds to them.\\n\\nC. Another powerful source of information for infants about the effects they can have on the world around them is provided when others mimic them. Many parents spend a lot of time, particularly in the early months, copying their infant's vocalizations and expressions. In addition, young children enjoy looking in mirrors, where the movements they can see are dependent upon their own movements. This is not to say that infants recognize the reflection as their own image (a later development). However, Lewis and Brooks-Gunn (1979) suggest that infants' developing understanding that the movements they see in the mirror are contingent on their own, leads to a growing awareness that they are distinct from other people. This is because they, and only they, can change the reflection in the mirror.\\n\\nD. This understanding that children gain of themselves as active agents continues to develop in their attempts to co-operate with others in play. Dunn (1988) points out that it is in such day-to-day relationships and interactions that the child's understanding of his- or herself emerges. Empirical investigations of the self-as-subject in young children are, however, rather scarce because of difficulties of communication: even if they can talk, young infants cannot verbalize very easily what they know about themselves.\\n\\nE. Once children have acquired a certain level of self-awareness, they begin to place themselves in a whole series of categories, which together play such an important part in defining them uniquely as 'themselves'. This second step in the development of a full sense of self is what James called the 'self-as-object'. This has been seen by many to be the aspect of the self which is most influenced by social elements, since it is made up of social roles (such as student, brother, colleague) and characteristics which derive their meaning from comparison or interaction with other people (such as trustworthiness, shyness, sporting ability).\\n\\nF. Cooley and other researchers suggested a close connection between a person's own understanding of their identity and other people's understanding of it. Cooley believed that people build up their sense of identity from the reactions of others to them, and from the view they believe others have of them. He called the self-as-object the 'looking-glass self', since people come to see themselves as they are reflected in others. Mead (1934) went even further, and saw the self and the social world as inextricably bound together: 'The self is essentially a social structure, and it arises in social experience ... it is impossible to conceive of a self arising outside of social experience.'\\n\\nG. Lewis and Brooks-Gunn argued that an important developmental milestone is reached when children become able to recognize themselves visually without the support of seeing contingent movement. This recognition occurs around their second birthday. In one experiment, Lewis and Brooks-Gunn (1979) dabbed some red powder on the noses of children who were playing in front of a mirror, and then observed how often they touched their noses. The psychologists reasoned that if the children knew what they usually looked like, they would be surprised by the unusual red mark and would start touching it. On the other hand, they found that children of 15 to 18 months are generally not able to recognize themselves unless other cues such as movement are present.\\n\\nH. Finally, perhaps the most graphic expressions of self-awareness in general can be seen in the displays of rage which are most common from 18 months to 3 years of age. In a longitudinal study of groups of three or four children, Bronson (1975) found that the intensity of the frustration and anger in their disagreements increased sharply between the ages of 1 and 2 years. Often, the children's disagreements involved a struggle over a toy that none of them had played with before or after the tug-of-war: the children seemed to be disputing ownership rather than wanting to play with it. Although it may be less marked in other societies, the link between the sense of 'self' and of 'ownership' is a notable feature of childhood in Western societies.\",\n            questions: [\n                {\n                    id: 14,\n                    type: 'MATCHING_HEADINGS',\n                    text: \"an account of the method used by researchers in a particular study\",\n                    correctAnswer: \"G\",\n                    explanation: \"Paragraph G describes Lewis and Brooks-Gunn's experiment with red powder on children's noses.\"\n                },\n                {\n                    id: 15,\n                    type: 'MATCHING_HEADINGS',\n                    text: \"the role of imitation in developing a sense of identity\",\n                    correctAnswer: \"C\",\n                    explanation: \"Paragraph C discusses how parents mimic infants and how this helps develop self-awareness.\"\n                },\n                {\n                    id: 16,\n                    type: 'MATCHING_HEADINGS',\n                    text: \"the age at which children can usually identify a static image of themselves\",\n                    correctAnswer: \"G\",\n                    explanation: \"Paragraph G states 'This recognition occurs around their second birthday'.\"\n                },\n                {\n                    id: 17,\n                    type: 'MATCHING_HEADINGS',\n                    text: \"a reason for the limitations of scientific research into 'self-as-subject'\",\n                    correctAnswer: \"D\",\n                    explanation: \"Paragraph D mentions 'difficulties of communication: even if they can talk, young infants cannot verbalize very easily what they know about themselves'.\"\n                },\n                {\n                    id: 18,\n                    type: 'MATCHING_HEADINGS',\n                    text: \"reference to a possible link between culture and a particular form of behaviour\",\n                    correctAnswer: \"H\",\n                    explanation: \"Paragraph H states 'Although it may be less marked in other societies, the link between the sense of 'self' and of 'ownership' is a notable feature of childhood in Western societies'.\"\n                },\n                {\n                    id: 19,\n                    type: 'MATCHING_HEADINGS',\n                    text: \"examples of the wide range of features that contribute to the sense of 'self-as-object'\",\n                    correctAnswer: \"E\",\n                    explanation: \"Paragraph E lists 'social roles (such as student, brother, colleague) and characteristics which derive their meaning from comparison or interaction with other people (such as trustworthiness, shyness, sporting ability)'.\"\n                },\n                {\n                    id: 21,\n                    type: 'MATCHING_HEADINGS',\n                    text: \"Choose the correct heading for section A and move it into the gap.\",\n                    headings: [\n                        \"How a concept from one field of study was applied in another\",\n                        \"A lack of investment in driver training\",\n                        \"Areas of doubt and disagreement between experts\",\n                        \"How different countries have dealt with traffic congestion\",\n                        \"The impact of driver behavior on traffic speed\",\n                        \"A proposal to take control away from the driver\"\n                    ],\n                    usedHeadings: [\n                        2,\n                        4,\n                        5,\n                        6\n                    ] // These headings are already used\n                },\n                {\n                    id: 22,\n                    type: 'MULTIPLE_SELECTION',\n                    text: \"Which TWO statements reflect civil engineers' opinions of the physicists' theories?\",\n                    selectCount: 2,\n                    options: [\n                        \"They fail to take into account road maintenance.\",\n                        \"They may have little to do with everyday traffic behaviour.\",\n                        \"They are inconsistent with chaos theory.\",\n                        \"They do not really describe anything new.\",\n                        \"They can easily be disproved.\"\n                    ]\n                }\n            ]\n        },\n        {\n            partNumber: 3,\n            title: \"The Impact of Social Media on Modern Communication\",\n            instructions: \"Read the text and answer questions 27–40.\",\n            questionRange: \"27–33\",\n            passage: \"Social media has fundamentally transformed the way humans communicate, creating unprecedented opportunities for connection while simultaneously raising concerns about the quality and authenticity of modern interactions. The rise of platforms such as Facebook, Twitter, Instagram, and TikTok has created a global communication network that operates 24 hours a day, seven days a week.\\n\\nOne of the most significant impacts of social media has been the democratization of information sharing. Previously, the dissemination of news and information was controlled by traditional media outlets such as newspapers, television, and radio stations. Today, any individual with internet access can share information instantly with a global audience. This has led to both positive and negative consequences. On the positive side, social movements have been able to organize more effectively, marginalized voices have found platforms to express themselves, and breaking news can be shared in real-time. However, this democratization has also led to the spread of misinformation and the creation of echo chambers where people are exposed only to information that confirms their existing beliefs.\\n\\nThe psychological impact of social media use has become a subject of intense research and debate. Studies have shown that excessive use of social media can lead to increased feelings of anxiety, depression, and social isolation, particularly among young people. The constant comparison with others' curated online personas can create unrealistic expectations and feelings of inadequacy. Furthermore, the addictive nature of social media platforms, designed to maximize user engagement through intermittent reinforcement schedules, has raised concerns about digital wellness and the need for better regulation of these technologies.\\n\\nDespite these concerns, social media has also created new opportunities for education, business, and creative expression. Online learning platforms have made education more accessible, small businesses can reach global markets through social media marketing, and artists and creators can build audiences without traditional gatekeepers. The COVID-19 pandemic highlighted the importance of digital communication tools in maintaining social connections during periods of physical isolation.\\n\\nLooking forward, the challenge will be to harness the benefits of social media while mitigating its negative effects. This will likely require a combination of technological solutions, regulatory frameworks, and digital literacy education to help users navigate the complex landscape of modern digital communication.\",\n            questions: [\n                {\n                    id: 27,\n                    type: 'MULTIPLE_CHOICE',\n                    text: \"According to the passage, social media has democratized information sharing by:\",\n                    options: [\n                        \"replacing traditional media outlets entirely\",\n                        \"allowing anyone with internet access to share information globally\",\n                        \"improving the quality of news reporting\",\n                        \"reducing the cost of information distribution\"\n                    ]\n                },\n                {\n                    id: 28,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Echo chambers are created when people only see information that supports their existing beliefs.\"\n                },\n                {\n                    id: 29,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Young people are more affected by social media's psychological impact than adults.\"\n                },\n                {\n                    id: 30,\n                    type: 'FILL_IN_BLANK',\n                    text: \"Social media platforms use _______ reinforcement schedules to maximize user engagement.\"\n                },\n                {\n                    id: 31,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"The COVID-19 pandemic proved that digital communication is superior to face-to-face interaction.\"\n                },\n                {\n                    id: 32,\n                    type: 'MULTIPLE_CHOICE',\n                    text: \"The passage suggests that addressing social media's negative effects will require:\",\n                    options: [\n                        \"banning social media platforms entirely\",\n                        \"only technological solutions\",\n                        \"a combination of technology, regulation, and education\",\n                        \"returning to traditional media only\"\n                    ]\n                },\n                {\n                    id: 33,\n                    type: 'SHORT_ANSWER',\n                    text: \"Name two positive outcomes of social media's democratization of information sharing mentioned in the passage.\"\n                },\n                {\n                    id: 34,\n                    type: 'SUMMARY_COMPLETION',\n                    text: \"For businesses, the use of complex language can have financial implications. The benefits of plain language can be seen in the case of companies who remove ___ from their forms and achieve ___ as a result.\\n\\nConsumers often complain that they experience a feeling of ___ when trying to put together do-it-yourself products which have not been tested by companies on a ___. In situations where not keeping to the correct procedures could affect safety issues, it is especially important that ___ information is not left out and no assumptions are made about a stage being self-evident or the consumer having a certain amount of ___.\\n\\nLawyers, however, have raised objections to the use of plain English. They feel that it would result in ambiguity in documents and cause people to lose faith in ___, as it would mean departing from language that has been used in the courts for a very long time.\",\n                    maxWords: 2\n                }\n            ]\n        }\n    ]\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/data/authentic-reading-test.ts\n"));

/***/ })

});